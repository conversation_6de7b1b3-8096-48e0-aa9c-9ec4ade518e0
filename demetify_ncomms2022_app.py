"""
Demetify - NCOMMS2022 Alzheimer's Disease Assessment Frontend
Professional radiologist-focused interface for AD/CN classification and interpretability
"""

import streamlit as st
import numpy as np
import nibabel as nib
import matplotlib.pyplot as plt
from pathlib import Path
import tempfile
import os
import logging
from typing import Optional, Dict, Any
import time

# Import nilearn for proper MRI visualization
try:
    from nilearn import plotting, image
    from nilearn.plotting import plot_anat, plot_stat_map
    NILEARN_AVAILABLE = True
except ImportError:
    NILEARN_AVAILABLE = False
    st.warning("Nilearn not available. Install with: pip install nilearn")

# Import our custom components
from ncomms2022_preprocessing_fsl import NCOM<PERSON><PERSON>LPreprocessor
from ncomms2022_model_enhanced import NCOMMSClassifier
from ncomms2022_shap import NC<PERSON>MSSHAPExplainer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="Demetify - AD Assessment",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for Demetify branding
st.markdown("""
<style>
    .main-header {
        background: linear-gradient(90deg, #1f4e79 0%, #2d5aa0 100%);
        padding: 1rem;
        border-radius: 10px;
        margin-bottom: 2rem;
    }
    .main-header h1 {
        color: white;
        text-align: center;
        margin: 0;
        font-size: 2.5rem;
    }
    .main-header p {
        color: #e8f4f8;
        text-align: center;
        margin: 0.5rem 0 0 0;
        font-size: 1.1rem;
    }
    .metric-card {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #1f4e79;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        margin: 0.5rem 0;
    }
    .risk-high { border-left-color: #d32f2f; }
    .risk-moderate { border-left-color: #f57c00; }
    .risk-low { border-left-color: #388e3c; }
    .processing-status {
        background: #f5f5f5;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #2196f3;
    }
</style>
""", unsafe_allow_html=True)

# Header
st.markdown("""
<div class="main-header">
    <h1>🧠 Demetify</h1>
    <p>Advanced Alzheimer's Disease Assessment System | University of Illinois at Urbana-Champaign</p>
    <p style="font-size: 0.9rem; margin-top: 0.5rem;">Project Lead: Prof. S. Seshadri | Radiologist Decision Support Tool</p>
</div>
""", unsafe_allow_html=True)

@st.cache_resource
def load_models():
    """Load and cache the models"""
    try:
        with st.spinner("Loading AI models..."):
            classifier = NCOMMSClassifier()
            explainer = NCOMMSSHAPExplainer(classifier)
            preprocessor = NCOMMSFSLPreprocessor()
        return classifier, explainer, preprocessor
    except Exception as e:
        st.error(f"Error loading models: {e}")
        return None, None, None

def display_mri_with_nilearn(mri_data: np.ndarray, title: str = "MRI Scan"):
    """Display MRI using nilearn for proper radiological orientation"""
    if not NILEARN_AVAILABLE:
        # Fallback to matplotlib
        fig, axes = plt.subplots(1, 3, figsize=(15, 5))
        
        # Axial (Bottom to Top view)
        axes[0].imshow(mri_data[:, :, mri_data.shape[2]//2], cmap='gray', origin='lower')
        axes[0].set_title('Axial')
        axes[0].axis('off')
        
        # Sagittal (Left to Right view)
        axes[1].imshow(mri_data[mri_data.shape[0]//2, :, :], cmap='gray', origin='lower')
        axes[1].set_title('Sagittal')
        axes[1].axis('off')
        
        # Coronal (Anterior to Posterior view)
        axes[2].imshow(mri_data[:, mri_data.shape[1]//2, :], cmap='gray', origin='lower')
        axes[2].set_title('Coronal')
        axes[2].axis('off')
        
        plt.suptitle(title)
        plt.tight_layout()
        st.pyplot(fig)
        plt.close()
    else:
        # Use nilearn for proper radiological display
        try:
            # Create temporary NIfTI file
            with tempfile.NamedTemporaryFile(suffix='.nii', delete=False) as tmp_file:
                img = nib.Nifti1Image(mri_data, affine=np.eye(4))
                nib.save(img, tmp_file.name)
                
                # Create nilearn plot
                fig = plt.figure(figsize=(15, 5))
                
                # Plot anatomical image with proper orientation
                plotting.plot_anat(
                    tmp_file.name,
                    figure=fig,
                    display_mode='ortho',
                    cut_coords=None,
                    title=title,
                    draw_cross=False,
                    annotate=True
                )
                
                st.pyplot(fig)
                plt.close()
                
                # Clean up
                os.unlink(tmp_file.name)
                
        except Exception as e:
            logger.error(f"Error with nilearn display: {e}")
            # Fallback to matplotlib
            display_mri_with_nilearn(mri_data, title)

def display_shap_overlay(mri_data: np.ndarray, shap_visualizations: Dict, title: str = "SHAP Interpretability"):
    """Display SHAP heatmap overlays"""
    st.subheader(title)

    if not shap_visualizations:
        st.warning("No interpretability visualizations available.")
        return

    # Create tabs for different views
    tab1, tab2, tab3 = st.tabs(["Axial", "Sagittal", "Coronal"])

    def safe_display_view(view_name, view_data):
        """Safely display a view with error handling"""
        try:
            if view_name in shap_visualizations and view_data:
                fig, axes = plt.subplots(1, 3, figsize=(15, 5))

                # Original MRI
                mri_slice = view_data['mri']
                axes[0].imshow(mri_slice, cmap='gray', origin='lower', vmin=0, vmax=1)
                axes[0].set_title('Original MRI')
                axes[0].axis('off')

                # SHAP Heatmap
                heatmap_slice = view_data['heatmap']
                if np.any(heatmap_slice):
                    axes[1].imshow(heatmap_slice, cmap='hot', origin='lower')
                    axes[1].set_title('SHAP Heatmap')
                else:
                    axes[1].imshow(np.zeros_like(mri_slice), cmap='hot', origin='lower')
                    axes[1].set_title('SHAP Heatmap (No significant regions)')
                axes[1].axis('off')

                # Overlay
                overlay_slice = view_data['overlay']
                if overlay_slice.ndim == 3:
                    axes[2].imshow(overlay_slice, origin='lower')
                else:
                    # Fallback: show original MRI
                    axes[2].imshow(mri_slice, cmap='gray', origin='lower', vmin=0, vmax=1)
                axes[2].set_title('MRI + SHAP Overlay')
                axes[2].axis('off')

                plt.suptitle(f'{view_name.title()} View - SHAP Analysis')
                plt.tight_layout()
                st.pyplot(fig)
                plt.close()
            else:
                st.warning(f"No {view_name} view data available.")
        except Exception as e:
            st.error(f"Error displaying {view_name} view: {str(e)}")

    with tab1:
        safe_display_view('axial', shap_visualizations.get('axial'))

    with tab2:
        safe_display_view('sagittal', shap_visualizations.get('sagittal'))

    with tab3:
        safe_display_view('coronal', shap_visualizations.get('coronal'))

def display_results(results: Dict):
    """Display classification results with professional styling"""
    st.subheader("🎯 Classification Results")
    
    # Main metrics
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        risk_class = "risk-" + results['risk_level'].lower()
        st.markdown(f"""
        <div class="metric-card {risk_class}">
            <h3>Risk Level</h3>
            <h2>{results['risk_level']}</h2>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown(f"""
        <div class="metric-card">
            <h3>Predicted Class</h3>
            <h2>{results['predicted_class']}</h2>
        </div>
        """, unsafe_allow_html=True)
    
    with col3:
        st.markdown(f"""
        <div class="metric-card">
            <h3>Confidence</h3>
            <h2>{results['confidence']:.1%}</h2>
        </div>
        """, unsafe_allow_html=True)
    
    with col4:
        st.markdown(f"""
        <div class="metric-card">
            <h3>COG Score</h3>
            <h2>{results['cog_score']:.1f}</h2>
        </div>
        """, unsafe_allow_html=True)
    
    # Detailed probabilities
    st.subheader("📊 Detailed Probabilities")
    col1, col2 = st.columns(2)
    
    with col1:
        st.metric("Normal Cognition (CN)", f"{results['cn_probability']:.1%}")
        st.progress(results['cn_probability'])
    
    with col2:
        st.metric("Alzheimer's Disease (AD)", f"{results['ad_probability']:.1%}")
        st.progress(results['ad_probability'])

def main():
    """Main application"""
    
    # Load models
    classifier, explainer, preprocessor = load_models()
    
    if classifier is None:
        st.error("Failed to load models. Please check the model files.")
        return
    
    # File upload section
    st.subheader("📁 Upload MRI Scan")
    uploaded_file = st.file_uploader(
        "Choose a T1-weighted MRI file (NIfTI format)",
        type=['nii', 'nii.gz'],
        help="Upload a T1-weighted structural MRI scan in NIfTI format"
    )
    
    if uploaded_file is not None:
        # Process the uploaded file
        with st.spinner("Processing MRI scan..."):
            try:
                # Save uploaded file temporarily
                with tempfile.NamedTemporaryFile(suffix='.nii', delete=False) as tmp_file:
                    tmp_file.write(uploaded_file.read())
                    tmp_file.flush()
                    
                    # Step 1: Preprocessing
                    st.markdown("""
                    <div class="processing-status">
                        <h4>🔄 Step 1: Preprocessing MRI Scan</h4>
                        <p>Applying FSL-based preprocessing pipeline...</p>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    processed_path = preprocessor.preprocess_single_scan(tmp_file.name)
                    
                    if processed_path is None:
                        st.error("Preprocessing failed. Please check the input file.")
                        return
                    
                    # Load preprocessed data
                    mri_data = np.load(processed_path)
                    
                    # Display original MRI
                    st.subheader("🧠 Preprocessed MRI Scan")
                    display_mri_with_nilearn(mri_data, "Preprocessed T1-weighted MRI")
                    
                    # Step 2: Classification
                    st.markdown("""
                    <div class="processing-status">
                        <h4>🤖 Step 2: AI Classification</h4>
                        <p>Running deep learning models for AD/CN classification...</p>
                    </div>
                    """, unsafe_allow_html=True)
                    
                    results = classifier.predict_full(mri_data)
                    display_results(results)
                    
                    # Step 3: Interpretability
                    st.markdown("""
                    <div class="processing-status">
                        <h4>🔍 Step 3: Generating Interpretability Maps</h4>
                        <p>Creating SHAP-based explanations...</p>
                    </div>
                    """, unsafe_allow_html=True)

                    # Add progress bar and timeout handling
                    progress_bar = st.progress(0)
                    status_text = st.empty()

                    try:
                        status_text.text("Initializing interpretability analysis...")
                        progress_bar.progress(10)

                        # Generate SHAP explanation with timeout
                        status_text.text("Generating gradient-based explanations...")
                        progress_bar.progress(30)

                        # Use simpler gradient-based explanation directly
                        add_shap = explainer._gradient_explanation(mri_data, 'ADD')
                        progress_bar.progress(70)

                        status_text.text("Creating visualization overlays...")
                        visualizations = explainer.generate_slice_visualizations(mri_data, add_shap)
                        progress_bar.progress(90)

                        status_text.text("Rendering interpretability maps...")
                        if visualizations:
                            display_shap_overlay(mri_data, visualizations, "SHAP Analysis - AD Classification")
                        progress_bar.progress(100)

                        # Clear progress indicators
                        progress_bar.empty()
                        status_text.empty()

                    except Exception as e:
                        progress_bar.empty()
                        status_text.empty()
                        st.warning(f"Interpretability generation encountered an issue: {str(e)}")
                        st.info("Continuing with classification results. Interpretability maps may be unavailable for this scan.")
                    
                    # Clean up
                    os.unlink(tmp_file.name)
                    if os.path.exists(processed_path):
                        os.unlink(processed_path)
                    
                    st.success("✅ Analysis completed successfully!")
                    
            except Exception as e:
                st.error(f"Error processing MRI scan: {e}")
                logger.error(f"Processing error: {e}")

if __name__ == "__main__":
    main()
