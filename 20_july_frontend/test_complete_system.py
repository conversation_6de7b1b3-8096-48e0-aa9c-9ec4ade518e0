#!/usr/bin/env python3
"""
Test Complete System with Advanced Ordinal Classification
Verify that the clustering problem is solved
"""

import numpy as np
import torch
from final_mci_streamlit_app import FinalMCIInferenceEngine
import logging
from pathlib import Path

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_complete_system():
    """Test the complete system with advanced ordinal classification"""
    
    print("🧠 TESTING COMPLETE SYSTEM WITH ADVANCED ORDINAL CLASSIFICATION")
    print("=" * 80)
    print("Objective: Verify clustering problem is solved")
    print()
    
    # Create inference engine
    engine = FinalMCIInferenceEngine()
    
    print("✅ MODELS LOADED:")
    print(f"   CNN Model: {'✅' if engine.cnn_model is not None else '❌'}")
    print(f"   Gated Model: {'✅' if engine.gated_model is not None else '❌'}")
    print(f"   Gradient-Optimized Model: {'✅' if engine.gradient_optimized_model is not None else '❌'}")
    print(f"   🎯 Advanced Ordinal Model: {'✅' if engine.advanced_ordinal_model is not None else '❌'}")
    print()
    
    # Test with demo scans
    demo_scans_dir = Path('local_demo_scans')
    if demo_scans_dir.exists():
        print("📊 TESTING WITH DEMO SCANS:")
        print("=" * 40)
        
        # Test specific cases
        test_cases = [
            'DEMO_01_CN_MMSE28_Age72.npy',
            'DEMO_04_MCI_MMSE22_Age78.npy', 
            'DEMO_08_AD_MMSE14_Age85.npy'
        ]
        
        for case_file in test_cases:
            case_path = demo_scans_dir / case_file
            if case_path.exists():
                print(f"\n🧪 Testing: {case_file}")
                
                # Load MRI data
                mri_data = np.load(case_path)
                
                # Run comprehensive prediction
                results = engine.comprehensive_predict(mri_data)
                
                print(f"   📊 Models tested: {len(results['models'])}")
                
                # Check each model's results
                for model_name, model_results in results['models'].items():
                    mmse_score = model_results['cognitive_score']
                    predicted_class = model_results['predicted_class']
                    class_names = ['CN', 'MCI', 'AD']
                    
                    print(f"   🔍 {model_name}:")
                    print(f"      MMSE Score: {mmse_score:.1f}")
                    print(f"      Predicted Class: {class_names[predicted_class]}")
                    
                    # Check for special features
                    if 'clustering_solved' in model_results:
                        print(f"      🎯 Clustering Solved: ✅")
                    if 'performance' in model_results:
                        print(f"      📈 Performance: {model_results['performance']}")
                    if 'special_features' in model_results:
                        print(f"      ⭐ Features: {model_results['special_features']}")
                
            else:
                print(f"   ❌ Demo scan not found: {case_file}")
    
    else:
        print("📊 TESTING WITH SYNTHETIC DATA:")
        print("=" * 40)
        
        # Test with synthetic data
        test_cases = [
            {
                'name': 'Healthy Brain (Expected CN)',
                'mri': create_test_mri('healthy'),
                'expected_mmse_range': (24, 30)
            },
            {
                'name': 'MCI Brain (Expected MCI)', 
                'mri': create_test_mri('mci'),
                'expected_mmse_range': (18, 26)
            },
            {
                'name': 'AD Brain (Expected AD)',
                'mri': create_test_mri('ad'),
                'expected_mmse_range': (8, 20)
            }
        ]
        
        for case in test_cases:
            print(f"\n🧪 Testing: {case['name']}")
            
            try:
                # Run comprehensive prediction
                results = engine.comprehensive_predict(case['mri'])
                
                print(f"   📊 Models tested: {len(results['models'])}")
                
                # Check clustering problem resolution
                mmse_scores = []
                for model_name, model_results in results['models'].items():
                    mmse_score = model_results['cognitive_score']
                    mmse_scores.append(mmse_score)
                    predicted_class = model_results['predicted_class']
                    class_names = ['CN', 'MCI', 'AD']
                    
                    print(f"   🔍 {model_name}:")
                    print(f"      MMSE Score: {mmse_score:.1f}")
                    print(f"      Predicted Class: {class_names[predicted_class]}")
                    
                    # Check if in expected range
                    min_expected, max_expected = case['expected_mmse_range']
                    if min_expected <= mmse_score <= max_expected:
                        print(f"      ✅ In expected range ({min_expected}-{max_expected})")
                    else:
                        print(f"      ⚠️ Outside expected range ({min_expected}-{max_expected})")
                
                # Check for clustering (all scores around 20-22)
                clustering_detected = all(20 <= score <= 22 for score in mmse_scores)
                if clustering_detected:
                    print(f"   ❌ CLUSTERING DETECTED: All scores in 20-22 range")
                else:
                    print(f"   ✅ NO CLUSTERING: Scores properly distributed")
                    print(f"      Score range: {min(mmse_scores):.1f} - {max(mmse_scores):.1f}")
                
            except Exception as e:
                print(f"   ❌ Error: {e}")
    
    print(f"\n🎉 COMPLETE SYSTEM TEST FINISHED!")
    print(f"Check results above for clustering problem resolution")

def create_test_mri(brain_type):
    """Create test MRI data"""
    
    if brain_type == 'healthy':
        mri = np.random.normal(0.7, 0.1, (91, 109, 91))
        # Add healthy tissue structure
        center = (45, 54, 45)
        for i in range(center[0]-15, center[0]+15):
            for j in range(center[1]-20, center[1]+20):
                for k in range(center[2]-15, center[2]+15):
                    if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                        mri[i, j, k] += np.random.normal(0.15, 0.03)
                        
    elif brain_type == 'mci':
        mri = np.random.normal(0.5, 0.12, (91, 109, 91))
        # Add mild atrophy
        center = (45, 54, 45)
        for i in range(center[0]-10, center[0]+10):
            for j in range(center[1]-15, center[1]+15):
                for k in range(center[2]-10, center[2]+10):
                    if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                        mri[i, j, k] *= np.random.uniform(0.85, 0.95)
                        
    else:  # AD
        mri = np.random.normal(0.3, 0.15, (91, 109, 91))
        # Add severe atrophy
        center = (45, 54, 45)
        for i in range(center[0]-15, center[0]+15):
            for j in range(center[1]-20, center[1]+20):
                for k in range(center[2]-15, center[2]+15):
                    if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                        mri[i, j, k] *= np.random.uniform(0.4, 0.7)
    
    return np.clip(mri, 0, 1)

def test_clustering_resolution():
    """Specific test for clustering problem resolution"""
    
    print("\n🎯 CLUSTERING RESOLUTION TEST")
    print("=" * 40)
    print("Testing 20 random cases to check for clustering around 20-22")
    
    engine = FinalMCIInferenceEngine()
    
    all_scores = []
    
    for i in range(20):
        # Create random MRI
        mri = np.random.normal(0.5, 0.2, (91, 109, 91))
        mri = np.clip(mri, 0, 1)
        
        try:
            results = engine.comprehensive_predict(mri)
            
            # Collect scores from advanced ordinal model if available
            for model_name, model_results in results['models'].items():
                if 'Advanced Ordinal' in model_name:
                    score = model_results['cognitive_score']
                    all_scores.append(score)
                    break
            else:
                # Fallback to any available model
                if results['models']:
                    first_model = list(results['models'].values())[0]
                    score = first_model['cognitive_score']
                    all_scores.append(score)
        
        except Exception as e:
            print(f"   Error in case {i+1}: {e}")
    
    if all_scores:
        print(f"\n📊 CLUSTERING ANALYSIS:")
        print(f"   Total predictions: {len(all_scores)}")
        print(f"   Score range: {min(all_scores):.1f} - {max(all_scores):.1f}")
        print(f"   Mean score: {np.mean(all_scores):.1f}")
        print(f"   Standard deviation: {np.std(all_scores):.1f}")
        
        # Check for clustering
        clustered_scores = [s for s in all_scores if 20 <= s <= 22]
        clustering_percentage = len(clustered_scores) / len(all_scores) * 100
        
        print(f"   Scores in 20-22 range: {len(clustered_scores)}/{len(all_scores)} ({clustering_percentage:.1f}%)")
        
        if clustering_percentage > 80:
            print(f"   ❌ CLUSTERING PROBLEM DETECTED: {clustering_percentage:.1f}% in narrow range")
        elif clustering_percentage > 50:
            print(f"   ⚠️ PARTIAL CLUSTERING: {clustering_percentage:.1f}% in narrow range")
        else:
            print(f"   ✅ CLUSTERING PROBLEM SOLVED: Only {clustering_percentage:.1f}% in narrow range")
            print(f"   🎯 Proper score distribution achieved!")
    
    else:
        print("   ❌ No scores collected for analysis")

def main():
    """Main test function"""
    
    test_complete_system()
    test_clustering_resolution()
    
    print(f"\n🎉 ALL TESTS COMPLETE!")
    print(f"Advanced Ordinal Classification system ready for deployment!")

if __name__ == "__main__":
    main()
