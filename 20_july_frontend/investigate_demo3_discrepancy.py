#!/usr/bin/env python3
"""
Investigate the discrepancy with demo3.npy predictions
"""

import sys
sys.path.append('demetify_deployment')
from ncomms2022_model import ModelManager
from ncomms2022_preprocessing import NCOMMs2022Preprocessor
import numpy as np

def investigate_demo3_predictions():
    """Investigate what's happening with demo3.npy predictions."""
    
    print("🔍 Investigating demo3.npy Prediction Discrepancy")
    print("=" * 60)
    
    try:
        # Load model and preprocessor
        manager = ModelManager()
        model = manager.load_model('CNN_baseline_new_cross0', device='cpu')
        preprocessor = NCOMMs2022Preprocessor()
        
        print("✅ Model and preprocessor loaded")
        
        # Test demo3.npy specifically
        demo_file = "ncomms2022/demo/mri/demo3.npy"
        
        print(f"\n📁 **Analyzing: {demo_file}**")
        print("Expected: Normal Cognition")
        print("-" * 50)
        
        # Step 1: Load and examine the raw data
        print("1️⃣ **Raw Data Analysis:**")
        raw_data = np.load(demo_file)
        print(f"   Shape: {raw_data.shape}")
        print(f"   Data range: {raw_data.min():.3f} to {raw_data.max():.3f}")
        print(f"   Mean: {raw_data.mean():.3f}")
        print(f"   Std: {raw_data.std():.3f}")
        
        # Step 2: Preprocess
        print("\n2️⃣ **Preprocessing:**")
        processed_data = preprocessor.preprocess_mri(
            demo_file,
            file_type='npy',
            apply_skull_stripping=False,
            apply_normalization=False
        )
        
        if processed_data is not None:
            print(f"   ✅ Preprocessing successful")
            print(f"   Processed shape: {processed_data.shape}")
            print(f"   Processed range: {processed_data.min():.3f} to {processed_data.max():.3f}")
            
            # Step 3: Get raw model predictions
            print("\n3️⃣ **Raw Model Predictions:**")
            predictions = model.predict_single(processed_data)
            
            if predictions:
                # ADD Prediction
                if 'ADD' in predictions:
                    add_result = predictions['ADD']
                    print(f"   ADD Probability: {add_result['probability']:.1%}")
                    print(f"   ADD Prediction: {add_result['prediction']} ({'AD' if add_result['prediction'] == 1 else 'Normal'})")
                    print(f"   ADD Confidence: {add_result['confidence']:.1%}")
                
                # COG Prediction
                if 'COG' in predictions:
                    cog_result = predictions['COG']
                    print(f"   COG Score: {cog_result['score']:.3f}")
                
                # Step 4: Analyze the discrepancy
                print("\n4️⃣ **Discrepancy Analysis:**")
                
                if 'ADD' in predictions and 'COG' in predictions:
                    add_pred = predictions['ADD']['prediction']
                    add_prob = predictions['ADD']['probability']
                    cog_score = predictions['COG']['score']
                    
                    print(f"   Expected: Normal case (ADD=0, low COG score)")
                    print(f"   Actual ADD: {add_pred} ({'AD' if add_pred == 1 else 'Normal'})")
                    print(f"   Actual COG: {cog_score:.3f}")
                    
                    # Check if this is actually mislabeled
                    if add_pred == 1:  # Predicting AD
                        print(f"   ⚠️ **ISSUE**: Model predicts AD for supposedly normal case")
                        print(f"   🤔 **Possible explanations:**")
                        print(f"      1. demo3.npy might actually be an AD case (mislabeled)")
                        print(f"      2. Model has issues with this specific scan")
                        print(f"      3. Preprocessing affecting the data")
                        
                        # Check if ADD probability is borderline
                        if 0.4 <= add_prob <= 0.6:
                            print(f"   📊 ADD probability {add_prob:.1%} is borderline - uncertain case")
                        elif add_prob > 0.8:
                            print(f"   📊 ADD probability {add_prob:.1%} is high - strong AD signal")
                        
                        # Check COG score consistency
                        if cog_score > 0.5:
                            print(f"   📊 COG score {cog_score:.3f} is elevated - consistent with ADD prediction")
                        else:
                            print(f"   📊 COG score {cog_score:.3f} is low - inconsistent with ADD prediction")
                    
                    else:
                        print(f"   ✅ Model correctly predicts Normal")
                
            else:
                print("   ❌ No predictions returned")
        else:
            print("   ❌ Preprocessing failed")
        
        return True
        
    except Exception as e:
        print(f"❌ Error during investigation: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_other_demo_files():
    """Check the other demo files for comparison."""
    
    print(f"\n🔬 **Checking Other Demo Files for Comparison:**")
    print("=" * 60)
    
    try:
        manager = ModelManager()
        model = manager.load_model('CNN_baseline_new_cross0', device='cpu')
        preprocessor = NCOMMs2022Preprocessor()
        
        demo_files = [
            ("ncomms2022/demo/mri/demo1.npy", "AD"),
            ("ncomms2022/demo/mri/demo2.npy", "AD"),
            ("ncomms2022/demo/mri/demo3.npy", "Normal")
        ]
        
        print(f"{'File':<15} {'Expected':<10} {'ADD Pred':<10} {'ADD Prob':<12} {'COG Score':<12} {'Consistent?':<12}")
        print("-" * 80)
        
        for demo_file, expected in demo_files:
            try:
                processed_data = preprocessor.preprocess_mri(
                    demo_file,
                    file_type='npy',
                    apply_skull_stripping=False,
                    apply_normalization=False
                )
                
                if processed_data is not None:
                    predictions = model.predict_single(processed_data)
                    
                    if predictions and 'ADD' in predictions and 'COG' in predictions:
                        add_pred = predictions['ADD']['prediction']
                        add_prob = predictions['ADD']['probability']
                        cog_score = predictions['COG']['score']
                        
                        filename = demo_file.split('/')[-1]
                        add_pred_str = "AD" if add_pred == 1 else "Normal"
                        
                        # Check consistency
                        if expected == "AD":
                            consistent = "✅" if add_pred == 1 and cog_score > 0.4 else "❌"
                        else:  # Normal
                            consistent = "✅" if add_pred == 0 and cog_score < 0.5 else "❌"
                        
                        print(f"{filename:<15} {expected:<10} {add_pred_str:<10} {add_prob:<12.1%} {cog_score:<12.3f} {consistent:<12}")
                        
            except Exception as e:
                print(f"{demo_file:<15} ERROR: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking demo files: {e}")
        return False

def provide_recommendations():
    """Provide recommendations based on the investigation."""
    
    print(f"\n💡 **Recommendations:**")
    print("=" * 60)
    
    print(f"""
🎯 **Based on Investigation Results:**

1️⃣ **If demo3.npy consistently shows AD prediction:**
   - This file might actually be an AD case, not Normal
   - The labeling in the demo might be incorrect
   - Keep the model predictions as they are (they might be correct)

2️⃣ **If predictions are borderline/uncertain:**
   - This represents a challenging case
   - Model uncertainty is normal for borderline cases
   - Consider adding confidence thresholds

3️⃣ **If there's clear model error:**
   - Check preprocessing pipeline
   - Verify model loading
   - Consider model retraining needs

✅ **Recommended Action:**
   - Keep the current code as-is
   - The model predictions might be more accurate than the demo labels
   - Add confidence indicators to help users understand uncertainty
   - Document that demo3.npy shows mixed signals (possibly borderline case)

🔍 **For Production Use:**
   - Focus on real clinical scans rather than demo files
   - Use confidence levels to indicate prediction certainty
   - Always recommend clinical correlation for borderline cases
""")

def main():
    """Main investigation function."""
    
    print("🎯 Demo3.npy Prediction Discrepancy Investigation")
    print("=" * 70)
    
    # Investigate demo3 specifically
    success1 = investigate_demo3_predictions()
    
    # Check other demo files for comparison
    success2 = check_other_demo_files()
    
    # Provide recommendations
    provide_recommendations()
    
    if success1 and success2:
        print(f"\n🏆 **Investigation Complete!**")
        print(f"Check the analysis above to understand the discrepancy.")
        print(f"The model predictions might actually be correct.")
    else:
        print(f"\n❌ **Investigation had issues - check errors above**")

if __name__ == "__main__":
    main()
