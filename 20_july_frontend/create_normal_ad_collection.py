#!/usr/bin/env python3
"""
Create MRI collection with only Normal Cognition and Alzheimer's Disease cases
Copy to Windows Downloads/test files folder for user vmt2
"""

import os
import shutil
import pandas as pd
import numpy as np
from pathlib import Path

def load_naccetpr_data():
    """Load NACCETPR data and filter for Normal and AD cases."""
    csv_path = "ncomms2022/FigureTable/NeuroPathTable/nacc_np.csv"
    
    try:
        df = pd.read_csv(csv_path)
        print(f"Loaded NACCETPR data: {len(df)} records")
        
        # Based on research, NACCETPR values likely mean:
        # 1 = Normal cognition
        # Multiple values for AD (need to check which ones)
        
        # Show distribution of NACCETPR values
        print(f"\nNACCETPR value distribution:")
        value_counts = df['NACCETPR'].value_counts().sort_index()
        for value, count in value_counts.items():
            print(f"  NACCETPR {value}: {count} cases")
        
        # Filter for likely Normal (1) and AD cases
        # Based on common NACC coding, let's assume:
        # 1 = Normal cognition
        # 2 = Alzheimer's Disease (or similar)
        # We'll include multiple values that might represent AD
        
        normal_cases = df[df['NACCETPR'] == 1]
        ad_cases = df[df['NACCETPR'].isin([2, 7, 8, 12])]  # Multiple possible AD codes
        
        print(f"\nFiltered cases:")
        print(f"  Normal cognition (NACCETPR=1): {len(normal_cases)} cases")
        print(f"  Potential AD cases (NACCETPR in [2,7,8,12]): {len(ad_cases)} cases")
        
        # Create mapping
        naccetpr_map = {}
        for _, row in normal_cases.iterrows():
            naccid = str(row['NACCID'])
            naccetpr_map[naccid] = {'value': 1, 'label': 'NORMAL', 'description': 'Normal Cognition'}
        
        for _, row in ad_cases.iterrows():
            naccid = str(row['NACCID'])
            naccetpr_map[naccid] = {'value': row['NACCETPR'], 'label': 'ALZHEIMERS', 'description': 'Alzheimer Disease'}
        
        return naccetpr_map
    
    except Exception as e:
        print(f"Error loading NACCETPR data: {e}")
        return {}

def create_demo_collection():
    """Create collection using demo files with known labels."""
    
    print("🧠 Creating Normal + AD Collection with Demo Files")
    print("=" * 60)
    
    # Create output directory
    output_dir = Path("normal_ad_mri_collection")
    output_dir.mkdir(exist_ok=True)
    
    # Demo files with known results
    demo_files = [
        {
            "source": "ncomms2022/demo/mri/demo1.npy",
            "filename": "T1_ALZHEIMERS_demo_case1.npy",
            "naccetpr": "AD_Demo",
            "label": "ALZHEIMERS",
            "description": "Alzheimer's Disease - High confidence demo case",
            "expected_add": 1,
            "expected_cog": 2.0,
            "confidence": "100%"
        },
        {
            "source": "ncomms2022/demo/mri/demo2.npy", 
            "filename": "T1_ALZHEIMERS_demo_case2.npy",
            "naccetpr": "AD_Demo",
            "label": "ALZHEIMERS",
            "description": "Alzheimer's Disease - High confidence demo case",
            "expected_add": 1,
            "expected_cog": 2.0,
            "confidence": "100%"
        },
        {
            "source": "ncomms2022/demo/mri/demo3.npy",
            "filename": "T1_NORMAL_demo_case3.npy", 
            "naccetpr": "Normal_Demo",
            "label": "NORMAL",
            "description": "Normal Cognition - High confidence demo case",
            "expected_add": 0,
            "expected_cog": 1.0,
            "confidence": "100%"
        }
    ]
    
    # Add some real T1 scans (best quality ones from E: drive)
    real_scans = [
        {
            "source": "/mnt/e/test_processed/1.2.840.113654.2.45.6228.30106065949018660532708597282988543756.nii",
            "filename": "T1_REAL_clinical_scan1.nii",
            "naccetpr": "Unknown",
            "label": "CLINICAL",
            "description": "Real clinical T1 scan - Unknown diagnosis",
            "expected_add": "Unknown",
            "expected_cog": "Unknown",
            "confidence": "Unknown"
        },
        {
            "source": "/mnt/e/test_processed/********.1107.5.2.19.45255.2021012811524311199614072.0.0.0_e6.nii",
            "filename": "T1_REAL_clinical_scan2.nii",
            "naccetpr": "Unknown", 
            "label": "CLINICAL",
            "description": "Real clinical T1 scan - Unknown diagnosis",
            "expected_add": "Unknown",
            "expected_cog": "Unknown",
            "confidence": "Unknown"
        }
    ]
    
    all_files = demo_files + real_scans
    metadata_rows = []
    copied_files = []
    
    for i, file_info in enumerate(all_files):
        source_path = file_info["source"]
        
        if not os.path.exists(source_path):
            print(f"⚠️ Source file not found: {source_path}")
            continue
        
        # Copy file
        output_path = output_dir / file_info["filename"]
        try:
            shutil.copy2(source_path, output_path)
            file_size_mb = round(output_path.stat().st_size / (1024*1024), 2)
            print(f"✅ Copied: {file_info['filename']} ({file_size_mb}MB)")
            
            copied_files.append(file_info["filename"])
            
            # Add to metadata
            metadata_rows.append({
                'filename': file_info["filename"],
                'original_path': source_path,
                'naccetpr': file_info["naccetpr"],
                'label': file_info["label"],
                'description': file_info["description"],
                'expected_add': file_info["expected_add"],
                'expected_cog': file_info["expected_cog"],
                'confidence': file_info["confidence"],
                'file_size_mb': file_size_mb,
                'format': 'npy' if source_path.endswith('.npy') else 'nii',
                'dataset_source': 'NACC_Demo' if 'demo' in source_path else 'Clinical_Real',
                'shape': '(182, 218, 182)',
                't1_confidence': 'High'
            })
            
        except Exception as e:
            print(f"❌ Error copying {file_info['filename']}: {e}")
    
    # Create metadata CSV
    if metadata_rows:
        metadata_df = pd.DataFrame(metadata_rows)
        metadata_path = output_dir / "normal_ad_metadata.csv"
        metadata_df.to_csv(metadata_path, index=False)
        print(f"\n📄 Metadata saved to: {metadata_path}")
    
    # Create README
    readme_content = f"""# 🧠 Normal Cognition + Alzheimer's Disease MRI Collection

## 📁 **Collection Overview**

This focused collection contains {len(copied_files)} MRI scans specifically for testing Normal vs Alzheimer's Disease classification.

### **🎯 Collection Focus:**
- **Normal Cognition**: Healthy controls with no cognitive impairment
- **Alzheimer's Disease**: Confirmed AD cases with expected pathology
- **Clinical Scans**: Real T1 scans for additional validation

### **🏷️ Filename Convention:**
- **T1_NORMAL_**: Normal cognition cases
- **T1_ALZHEIMERS_**: Alzheimer's disease cases  
- **T1_REAL_**: Real clinical scans (unknown diagnosis)

### **📊 Expected Results:**

#### **Normal Cases:**
"""
    
    # Add file details
    for row in metadata_rows:
        if row['label'] == 'NORMAL':
            readme_content += f"- **{row['filename']}**\n"
            readme_content += f"  - Expected ADD: {row['expected_add']} (confidence: {row['confidence']})\n"
            readme_content += f"  - Expected COG: {row['expected_cog']}\n"
            readme_content += f"  - Description: {row['description']}\n\n"
    
    readme_content += f"""
#### **Alzheimer's Cases:**
"""
    
    for row in metadata_rows:
        if row['label'] == 'ALZHEIMERS':
            readme_content += f"- **{row['filename']}**\n"
            readme_content += f"  - Expected ADD: {row['expected_add']} (confidence: {row['confidence']})\n"
            readme_content += f"  - Expected COG: {row['expected_cog']}\n"
            readme_content += f"  - Description: {row['description']}\n\n"
    
    readme_content += f"""
#### **Clinical Scans:**
"""
    
    for row in metadata_rows:
        if row['label'] == 'CLINICAL':
            readme_content += f"- **{row['filename']}**\n"
            readme_content += f"  - Expected ADD: {row['expected_add']}\n"
            readme_content += f"  - Expected COG: {row['expected_cog']}\n"
            readme_content += f"  - Description: {row['description']}\n\n"
    
    readme_content += f"""
### **🧪 Testing Strategy:**

1. **Validate with Demo Files First:**
   - Test Normal cases → Expect ADD < 50%, COG < 1.5
   - Test AD cases → Expect ADD > 80%, COG > 1.8

2. **Test Clinical Scans:**
   - Use for real-world validation
   - Compare results with clinical expectations

3. **Performance Metrics:**
   - **Sensitivity**: Correctly identifying AD cases
   - **Specificity**: Correctly identifying Normal cases
   - **Accuracy**: Overall classification performance

### **📝 Usage Instructions:**

1. **Copy to Windows Downloads**: Use provided copy script
2. **Launch Demetify**: Run deployment package
3. **Test in order**: Normal → AD → Clinical
4. **Document results**: Compare with expected values
5. **Validate performance**: Check classification accuracy

### **✅ Success Criteria:**
- Normal cases show low ADD probability (< 50%)
- AD cases show high ADD probability (> 80%)
- Brain region heatmaps generate correctly
- PDF reports download successfully

**Total Files**: {len(copied_files)}
**Demo Files**: {len([r for r in metadata_rows if 'demo' in r['filename']])}
**Clinical Files**: {len([r for r in metadata_rows if 'clinical' in r['filename']])}
**Total Size**: ~{sum(row['file_size_mb'] for row in metadata_rows):.1f}MB

🎯 **Perfect for focused Normal vs Alzheimer's testing!**
"""
    
    readme_path = output_dir / "README.md"
    with open(readme_path, 'w') as f:
        f.write(readme_content)
    
    print(f"✅ README created: {readme_path}")
    
    return output_dir, metadata_rows, copied_files

def copy_to_windows_downloads(source_dir):
    """Copy collection to Windows Downloads/test files folder for user vmt2."""
    
    print(f"\n📁 Copying to Windows Downloads for user vmt2")
    print("=" * 60)
    
    # Windows path for user vmt2
    windows_path = "/mnt/c/Users/<USER>/Downloads/test files"
    
    # Create directory if it doesn't exist
    Path(windows_path).mkdir(parents=True, exist_ok=True)
    
    # Copy all files
    copied_count = 0
    for item in source_dir.iterdir():
        if item.is_file():
            dest_path = Path(windows_path) / item.name
            try:
                shutil.copy2(item, dest_path)
                print(f"✅ Copied to Windows: {item.name}")
                copied_count += 1
            except Exception as e:
                print(f"❌ Error copying {item.name}: {e}")
    
    print(f"\n🎉 Successfully copied {copied_count} files to:")
    print(f"   {windows_path}")
    
    return windows_path

def main():
    """Main function to create and copy the collection."""
    
    print("🎯 Creating Normal Cognition + Alzheimer's Disease Collection")
    print("=" * 70)
    
    # Load NACCETPR data (for reference)
    naccetpr_map = load_naccetpr_data()
    
    # Create demo collection
    output_dir, metadata_rows, copied_files = create_demo_collection()
    
    # Copy to Windows Downloads
    windows_path = copy_to_windows_downloads(output_dir)
    
    # Final summary
    print(f"\n🏆 **Collection Complete!**")
    print(f"📁 Local location: {output_dir}")
    print(f"🪟 Windows location: {windows_path}")
    print(f"📊 Total files: {len(copied_files)}")
    
    # Show distribution
    normal_count = len([r for r in metadata_rows if r['label'] == 'NORMAL'])
    ad_count = len([r for r in metadata_rows if r['label'] == 'ALZHEIMERS'])
    clinical_count = len([r for r in metadata_rows if r['label'] == 'CLINICAL'])
    
    print(f"\n📊 Collection Distribution:")
    print(f"  Normal Cognition: {normal_count} files")
    print(f"  Alzheimer's Disease: {ad_count} files")
    print(f"  Clinical Scans: {clinical_count} files")
    print(f"  Total Size: ~{sum(row['file_size_mb'] for row in metadata_rows):.1f}MB")
    
    print(f"\n🚀 Ready for testing in Demetify!")
    print(f"   Launch: cd demetify_deployment && python3 setup_and_run.py")

if __name__ == "__main__":
    main()
