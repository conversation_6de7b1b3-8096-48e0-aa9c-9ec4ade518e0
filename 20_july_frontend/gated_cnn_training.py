#!/usr/bin/env python3
"""
Gated CNN Training for Enhanced MCI Classification
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
import numpy as np
import json
import time
from pathlib import Path
import logging

from segmentation_enhanced_model import SegmentationEnhancedMCIModel
from data_loader import MCIDataset, get_balanced_datasets

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def train_gated_cnn():
    """Train gated CNN model"""
    
    logger.info("🚀 Starting Gated CNN Training...")
    
    # Device setup
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    logger.info(f"Using device: {device}")
    
    # Load datasets
    train_dataset, val_dataset, test_dataset = get_balanced_datasets()
    
    # Data loaders
    train_loader = DataLoader(train_dataset, batch_size=4, shuffle=True, num_workers=4)
    val_loader = DataLoader(val_dataset, batch_size=4, shuffle=False, num_workers=4)
    test_loader = DataLoader(test_dataset, batch_size=4, shuffle=False, num_workers=4)
    
    logger.info(f"Train samples: {len(train_dataset)}")
    logger.info(f"Val samples: {len(val_dataset)}")
    logger.info(f"Test samples: {len(test_dataset)}")
    
    # Model
    model = SegmentationEnhancedMCIModel(num_classes=3).to(device)
    
    # Optimizer and scheduler
    optimizer = optim.AdamW(model.parameters(), lr=0.001, weight_decay=0.01)
    scheduler = optim.lr_scheduler.ReduceLROnPlateau(optimizer, mode='min', patience=5, factor=0.5)
    
    # Loss function
    class_weights = torch.tensor([1.0, 2.0, 1.0]).to(device)  # Weight MCI higher
    criterion = nn.CrossEntropyLoss(weight=class_weights)
    mse_criterion = nn.MSELoss()
    
    # Training parameters
    num_epochs = 50
    best_val_loss = float('inf')
    patience = 10
    patience_counter = 0
    
    # Training history
    history = {
        'train_loss': [],
        'train_acc': [],
        'val_loss': [],
        'val_acc': []
    }
    
    logger.info("Starting training...")
    start_time = time.time()
    
    for epoch in range(num_epochs):
        # Training phase
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for batch_idx, batch in enumerate(train_loader):
            mri_data = batch['mri'].to(device)
            labels = batch['label'].to(device)
            atrophy_scores = batch['atrophy_score'].to(device)
            clinical_scores = batch['clinical_scores'].to(device)
            
            optimizer.zero_grad()
            
            # Forward pass
            outputs = model(mri_data)
            
            # Multi-task loss
            cls_loss = criterion(outputs['classification'], labels)
            atrophy_loss = mse_criterion(outputs['atrophy'].squeeze(), atrophy_scores)
            clinical_loss = mse_criterion(outputs['clinical'], clinical_scores)
            
            # Combined loss
            total_loss = cls_loss + 0.3 * atrophy_loss + 0.2 * clinical_loss
            
            total_loss.backward()
            optimizer.step()
            
            # Statistics
            train_loss += total_loss.item()
            _, predicted = torch.max(outputs['classification'].data, 1)
            train_total += labels.size(0)
            train_correct += (predicted == labels).sum().item()
            
            if batch_idx % 20 == 0:
                logger.info(f'Epoch {epoch+1}, Batch {batch_idx}, Loss: {total_loss.item():.4f}')
        
        # Validation phase
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for batch in val_loader:
                mri_data = batch['mri'].to(device)
                labels = batch['label'].to(device)
                atrophy_scores = batch['atrophy_score'].to(device)
                clinical_scores = batch['clinical_scores'].to(device)
                
                outputs = model(mri_data)
                
                # Multi-task loss
                cls_loss = criterion(outputs['classification'], labels)
                atrophy_loss = mse_criterion(outputs['atrophy'].squeeze(), atrophy_scores)
                clinical_loss = mse_criterion(outputs['clinical'], clinical_scores)
                
                total_loss = cls_loss + 0.3 * atrophy_loss + 0.2 * clinical_loss
                
                val_loss += total_loss.item()
                _, predicted = torch.max(outputs['classification'].data, 1)
                val_total += labels.size(0)
                val_correct += (predicted == labels).sum().item()
        
        # Calculate averages
        train_loss /= len(train_loader)
        train_acc = train_correct / train_total
        val_loss /= len(val_loader)
        val_acc = val_correct / val_total
        
        # Update history
        history['train_loss'].append(train_loss)
        history['train_acc'].append(train_acc)
        history['val_loss'].append(val_loss)
        history['val_acc'].append(val_acc)
        
        logger.info(f'Epoch {epoch+1}/{num_epochs} - Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}, Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}')
        
        # Learning rate scheduling
        scheduler.step(val_loss)
        
        # Early stopping and model saving
        if val_loss < best_val_loss:
            best_val_loss = val_loss
            patience_counter = 0
            
            # Save best model
            torch.save({
                'epoch': epoch,
                'model_state_dict': model.state_dict(),
                'optimizer_state_dict': optimizer.state_dict(),
                'best_val_loss': best_val_loss,
                'history': history
            }, 'checkpoints/best_gated_cnn_model.pth')
            
            logger.info(f'New best model saved with val_loss: {best_val_loss:.4f}')
        else:
            patience_counter += 1
            
        if patience_counter >= patience:
            logger.info(f'Early stopping triggered after {epoch+1} epochs')
            break
    
    # Test evaluation
    logger.info("Evaluating on test set...")
    model.eval()
    test_correct = 0
    test_total = 0
    test_predictions = []
    test_labels = []
    
    with torch.no_grad():
        for batch in test_loader:
            mri_data = batch['mri'].to(device)
            labels = batch['label'].to(device)
            
            outputs = model(mri_data)
            _, predicted = torch.max(outputs['classification'].data, 1)
            
            test_total += labels.size(0)
            test_correct += (predicted == labels).sum().item()
            
            test_predictions.extend(predicted.cpu().numpy())
            test_labels.extend(labels.cpu().numpy())
    
    test_accuracy = test_correct / test_total
    
    # Training summary
    training_time = time.time() - start_time
    
    results = {
        'model_type': 'GatedCNN',
        'test_accuracy': test_accuracy,
        'best_val_loss': best_val_loss,
        'training_time': training_time,
        'epochs_trained': epoch + 1,
        'history': history
    }
    
    # Save results
    with open('results/gated_cnn_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    logger.info(f"🎉 Gated CNN Training completed!")
    logger.info(f"Test Accuracy: {test_accuracy:.4f}")
    logger.info(f"Training Time: {training_time:.1f}s")
    
    return results

if __name__ == "__main__":
    # Create directories
    Path('checkpoints').mkdir(exist_ok=True)
    Path('results').mkdir(exist_ok=True)
    Path('logs').mkdir(exist_ok=True)
    
    # Train model
    results = train_gated_cnn()
