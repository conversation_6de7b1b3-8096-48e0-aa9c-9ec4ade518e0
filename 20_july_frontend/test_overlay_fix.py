#!/usr/bin/env python3
"""
Test MRI Heatmap Overlay Fix
Verify that heatmaps are properly overlaid on MRI images
"""

import numpy as np
import matplotlib.pyplot as plt
from final_mci_streamlit_app import FinalMCIInferenceEngine, create_heatmap_visualization, create_single_slice_overlay

def test_overlay_visualization():
    """Test that heatmap overlay is working correctly"""
    
    print("🗺️ TESTING MRI HEATMAP OVERLAY FIX")
    print("=" * 50)
    print("Objective: Verify heatmaps are overlaid on MRI, not separate")
    print()
    
    # Create test MRI data
    test_mri = np.random.normal(0.6, 0.1, (91, 109, 91))
    test_mri = np.clip(test_mri, 0, 1)
    
    # Add some brain-like structure
    center = (45, 54, 45)
    for i in range(91):
        for j in range(109):
            for k in range(91):
                dist = np.sqrt((i-center[0])**2 + (j-center[1])**2 + (k-center[2])**2)
                if dist < 35:  # Brain region
                    test_mri[i, j, k] = 0.7 + 0.2 * np.random.random()
                else:  # Background
                    test_mri[i, j, k] = 0.1 * np.random.random()
    
    print(f"✅ Test MRI created: shape {test_mri.shape}")
    print(f"   MRI range: {test_mri.min():.3f} - {test_mri.max():.3f}")
    
    # Create inference engine
    engine = FinalMCIInferenceEngine()
    
    # Test different cognitive scores
    test_cases = [
        {'mmse': 28, 'expected': 'Minimal overlay'},
        {'mmse': 22, 'expected': 'Moderate overlay'},
        {'mmse': 18, 'expected': 'Significant overlay'},
        {'mmse': 12, 'expected': 'Extensive overlay'}
    ]
    
    for case in test_cases:
        print(f"\n🧪 Testing MMSE {case['mmse']} - {case['expected']}")
        
        # Generate heatmap
        heatmap = engine._generate_realistic_brain_heatmap(case['mmse'], test_mri)
        
        print(f"   Heatmap range: {heatmap.min():.3f} - {heatmap.max():.3f}")
        
        # Check overlay quality
        nonzero_voxels = np.count_nonzero(heatmap)
        total_voxels = heatmap.size
        activation_percentage = nonzero_voxels / total_voxels * 100
        
        print(f"   Activation: {activation_percentage:.2f}% of brain")
        
        # Test visualization creation
        try:
            fig = create_heatmap_visualization(test_mri, heatmap, case['mmse'], "Test Model")
            print(f"   ✅ Overlay visualization created successfully")
            
            # Check that we have both MRI and heatmap traces
            num_traces = len(fig.data)
            print(f"   📊 Number of traces: {num_traces}")
            
            if num_traces >= 3:  # At least MRI base for 3 views
                print(f"   ✅ Multiple traces found - overlay working")
            else:
                print(f"   ⚠️ Few traces - may not be overlaying properly")
                
        except Exception as e:
            print(f"   ❌ Visualization failed: {e}")
        
        # Test single slice overlay
        try:
            mid_slice = test_mri.shape[2] // 2
            single_fig = create_single_slice_overlay(test_mri, heatmap, mid_slice, 'axial', f"MMSE {case['mmse']} Overlay")
            print(f"   ✅ Single slice overlay created successfully")
            
            # Check traces in single slice
            single_traces = len(single_fig.data)
            print(f"   📊 Single slice traces: {single_traces}")
            
        except Exception as e:
            print(f"   ❌ Single slice overlay failed: {e}")

def test_comprehensive_overlay():
    """Test comprehensive prediction with overlay"""
    
    print(f"\n🔍 TESTING COMPREHENSIVE PREDICTION WITH OVERLAY")
    print("=" * 50)
    
    engine = FinalMCIInferenceEngine()
    
    # Create test MRI
    test_mri = np.random.normal(0.6, 0.1, (91, 109, 91))
    test_mri = np.clip(test_mri, 0, 1)
    
    try:
        # Run comprehensive prediction
        results = engine.comprehensive_predict(test_mri)
        
        print(f"✅ Comprehensive prediction successful")
        print(f"📊 Models tested: {len(results['models'])}")
        
        # Test overlay for each model
        for model_name, model_results in results['models'].items():
            heatmap = model_results['heatmap']
            mmse_score = model_results['cognitive_score']
            
            print(f"\n🔍 {model_name}:")
            print(f"   MMSE Score: {mmse_score:.1f}")
            
            # Check heatmap quality
            nonzero_voxels = np.count_nonzero(heatmap)
            total_voxels = heatmap.size
            activation_percentage = nonzero_voxels / total_voxels * 100
            
            print(f"   Heatmap activation: {activation_percentage:.2f}%")
            print(f"   Heatmap range: {heatmap.min():.3f} - {heatmap.max():.3f}")
            
            # Test overlay creation
            try:
                overlay_fig = create_heatmap_visualization(test_mri, heatmap, mmse_score, model_name)
                print(f"   ✅ Overlay visualization: SUCCESS")
                print(f"   📊 Traces: {len(overlay_fig.data)}")
                
                # Check for proper overlay structure
                if len(overlay_fig.data) >= 3:
                    print(f"   ✅ Multi-view overlay working")
                else:
                    print(f"   ⚠️ May not be properly overlaying")
                    
            except Exception as e:
                print(f"   ❌ Overlay creation failed: {e}")
    
    except Exception as e:
        print(f"❌ Comprehensive prediction failed: {e}")

def main():
    """Main test function"""
    
    print("🗺️ MRI HEATMAP OVERLAY FIX TEST")
    print("=" * 60)
    print("Testing that heatmaps are properly overlaid on MRI images")
    print("(Not shown as separate pixelated views)")
    print()
    
    test_overlay_visualization()
    test_comprehensive_overlay()
    
    print(f"\n🎉 OVERLAY FIX TEST COMPLETE!")
    print(f"Check results above - should see proper MRI+heatmap overlay")
    print(f"Frontend URL: http://0.0.0.0:8503")

if __name__ == "__main__":
    main()
