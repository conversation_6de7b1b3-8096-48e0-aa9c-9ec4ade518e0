#!/usr/bin/env python3
"""
Quick test to identify the best working models for MRI classification
"""

import numpy as np
import torch
import sys
from pathlib import Path
import json

# Add paths for model imports
sys.path.append('demetify_final_enhanced')
sys.path.append('ncomms2022')

def test_model_loading():
    """Test loading different available models"""
    
    print("🧠 Testing Available MRI Classification Models")
    print("=" * 60)
    
    # Test data - load a sample from our test collection
    test_mri_path = Path("experiment_25_scans/CASE_01_mri.npy")
    if test_mri_path.exists():
        test_mri = np.load(test_mri_path)
        print(f"✅ Loaded test MRI: {test_mri.shape}")
    else:
        print("❌ No test MRI found")
        return
    
    models_tested = []
    
    # Test 1: NCOMMs2022 Model (from demetify_final_enhanced)
    print("\n1. Testing NCOMMs2022 Model...")
    try:
        from ncomms2022_model import NCOMMs2022Model
        
        model = NCOMMs2022Model(device='cpu')
        checkpoint_dir = "demetify_final_enhanced/CNN_baseline_new_cross0"
        
        if Path(checkpoint_dir).exists():
            success = model.load_pretrained_weights(checkpoint_dir)
            if success:
                # Test inference
                mri_tensor = torch.FloatTensor(test_mri).unsqueeze(0).unsqueeze(0)
                with torch.no_grad():
                    outputs = model.predict(mri_tensor)
                    print(f"✅ NCOMMs2022 Model working - Output: {outputs}")
                    models_tested.append({
                        'name': 'NCOMMs2022',
                        'path': checkpoint_dir,
                        'status': 'working',
                        'output_format': type(outputs).__name__
                    })
            else:
                print("❌ Failed to load NCOMMs2022 weights")
                models_tested.append({'name': 'NCOMMs2022', 'status': 'failed_weights'})
        else:
            print("❌ NCOMMs2022 checkpoint directory not found")
            models_tested.append({'name': 'NCOMMs2022', 'status': 'no_checkpoint'})
            
    except Exception as e:
        print(f"❌ NCOMMs2022 Model failed: {e}")
        models_tested.append({'name': 'NCOMMs2022', 'status': 'error', 'error': str(e)})
    
    # Test 2: Final Improved CNN
    print("\n2. Testing Final Improved CNN...")
    try:
        final_cnn_path = Path("final_improved_cnn.pth")
        if final_cnn_path.exists():
            print(f"✅ Found Final Improved CNN: {final_cnn_path}")
            models_tested.append({
                'name': 'Final_Improved_CNN',
                'path': str(final_cnn_path),
                'status': 'found',
                'size_mb': final_cnn_path.stat().st_size / (1024*1024)
            })
        else:
            print("❌ Final Improved CNN not found")
            models_tested.append({'name': 'Final_Improved_CNN', 'status': 'not_found'})
            
    except Exception as e:
        print(f"❌ Final Improved CNN test failed: {e}")
        models_tested.append({'name': 'Final_Improved_CNN', 'status': 'error', 'error': str(e)})
    
    # Test 3: Final Improved Gated CNN
    print("\n3. Testing Final Improved Gated CNN...")
    try:
        gated_cnn_path = Path("final_improved_gated_cnn.pth")
        if gated_cnn_path.exists():
            print(f"✅ Found Final Improved Gated CNN: {gated_cnn_path}")
            models_tested.append({
                'name': 'Final_Improved_Gated_CNN',
                'path': str(gated_cnn_path),
                'status': 'found',
                'size_mb': gated_cnn_path.stat().st_size / (1024*1024)
            })
        else:
            print("❌ Final Improved Gated CNN not found")
            models_tested.append({'name': 'Final_Improved_Gated_CNN', 'status': 'not_found'})
            
    except Exception as e:
        print(f"❌ Final Improved Gated CNN test failed: {e}")
        models_tested.append({'name': 'Final_Improved_Gated_CNN', 'status': 'error', 'error': str(e)})
    
    # Test 4: Gradient Optimized Model
    print("\n4. Testing Gradient Optimized Model...")
    try:
        grad_model_path = Path("gradient_optimized_model.pth")
        if grad_model_path.exists():
            print(f"✅ Found Gradient Optimized Model: {grad_model_path}")
            models_tested.append({
                'name': 'Gradient_Optimized',
                'path': str(grad_model_path),
                'status': 'found',
                'size_mb': grad_model_path.stat().st_size / (1024*1024)
            })
        else:
            print("❌ Gradient Optimized Model not found")
            models_tested.append({'name': 'Gradient_Optimized', 'status': 'not_found'})
            
    except Exception as e:
        print(f"❌ Gradient Optimized Model test failed: {e}")
        models_tested.append({'name': 'Gradient_Optimized', 'status': 'error', 'error': str(e)})
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 MODEL TESTING SUMMARY")
    print("=" * 60)
    
    working_models = [m for m in models_tested if m.get('status') in ['working', 'found']]
    
    print(f"✅ Working/Available Models: {len(working_models)}")
    for model in working_models:
        print(f"   - {model['name']}: {model['status']}")
    
    failed_models = [m for m in models_tested if m.get('status') not in ['working', 'found']]
    print(f"❌ Failed Models: {len(failed_models)}")
    for model in failed_models:
        print(f"   - {model['name']}: {model['status']}")
    
    # Save results
    with open('model_test_results.json', 'w') as f:
        json.dump(models_tested, f, indent=2)
    
    print(f"\n💾 Results saved to model_test_results.json")
    
    return working_models

if __name__ == "__main__":
    working_models = test_model_loading()
