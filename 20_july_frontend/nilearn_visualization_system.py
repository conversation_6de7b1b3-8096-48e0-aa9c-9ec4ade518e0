#!/usr/bin/env python3
"""
Nilearn Visualization System for MRI and Heatmap Display
Maintains proper aspect ratios, no stretching, radiologist-friendly orientations
"""

import numpy as np
import matplotlib.pyplot as plt
import nibabel as nib
from nilearn import plotting, image
from nilearn.plotting import plot_stat_map, plot_anat
import tempfile
import os
from pathlib import Path
import logging
import io
import base64

logger = logging.getLogger(__name__)

class NilearnVisualizationSystem:
    """
    Professional MRI visualization system using nilearn
    - Maintains original aspect ratios
    - No image stretching
    - Radiologist-expected orientations
    - Clean overlay of heatmaps on original MRI
    """
    
    def __init__(self):
        self.temp_files = []  # Track temporary files for cleanup
        logger.info("🎨 Nilearn Visualization System initialized")
    
    def create_mri_visualization(self, mri_data, affine_matrix, title="MRI Scan"):
        """
        Create clean MRI visualization using nilearn
        
        Args:
            mri_data: 3D numpy array of MRI data
            affine_matrix: 4x4 affine transformation matrix
            title: Title for the visualization
            
        Returns:
            dict: Contains matplotlib figures for different views
        """
        logger.info(f"🧠 Creating MRI visualization: {mri_data.shape}")
        
        try:
            # Create nibabel image object
            nii_img = nib.Nifti1Image(mri_data, affine_matrix)
            
            # Create visualizations for different anatomical views
            views = {}
            
            # Axial view (radiologist standard)
            views['axial'] = self._create_axial_view(nii_img, title)
            
            # Sagittal view
            views['sagittal'] = self._create_sagittal_view(nii_img, title)
            
            # Coronal view
            views['coronal'] = self._create_coronal_view(nii_img, title)
            
            logger.info("✅ MRI visualization created successfully")
            return views
            
        except Exception as e:
            logger.error(f"❌ MRI visualization failed: {e}")
            raise
    
    def create_heatmap_overlay(self, mri_data, heatmap_data, affine_matrix, 
                              title="MRI with Heatmap", opacity=0.7):
        """
        Create heatmap overlay on MRI using nilearn
        
        Args:
            mri_data: 3D numpy array of MRI data
            heatmap_data: 3D numpy array of heatmap data
            affine_matrix: 4x4 affine transformation matrix
            title: Title for the visualization
            opacity: Heatmap opacity (0-1)
            
        Returns:
            dict: Contains matplotlib figures with overlays
        """
        logger.info(f"🔥 Creating heatmap overlay: MRI {mri_data.shape}, Heatmap {heatmap_data.shape}")
        
        try:
            # Create nibabel image objects
            mri_img = nib.Nifti1Image(mri_data, affine_matrix)
            heatmap_img = nib.Nifti1Image(heatmap_data, affine_matrix)
            
            # Create overlay visualizations
            overlays = {}
            
            # Axial overlay
            overlays['axial'] = self._create_axial_overlay(
                mri_img, heatmap_img, title, opacity
            )
            
            # Sagittal overlay
            overlays['sagittal'] = self._create_sagittal_overlay(
                mri_img, heatmap_img, title, opacity
            )
            
            # Coronal overlay
            overlays['coronal'] = self._create_coronal_overlay(
                mri_img, heatmap_img, title, opacity
            )
            
            logger.info("✅ Heatmap overlay created successfully")
            return overlays
            
        except Exception as e:
            logger.error(f"❌ Heatmap overlay failed: {e}")
            raise
    
    def _create_axial_view(self, nii_img, title):
        """Create axial view using nilearn"""
        fig = plt.figure(figsize=(12, 8))
        
        # Plot anatomical image
        display = plot_anat(
            nii_img,
            display_mode='z',
            cut_coords=5,
            title=f"{title} - Axial View",
            figure=fig,
            draw_cross=False,
            annotate=True,
            cmap='gray'
        )
        
        return fig
    
    def _create_sagittal_view(self, nii_img, title):
        """Create sagittal view using nilearn"""
        fig = plt.figure(figsize=(12, 8))
        
        # Plot anatomical image
        display = plot_anat(
            nii_img,
            display_mode='x',
            cut_coords=5,
            title=f"{title} - Sagittal View",
            figure=fig,
            draw_cross=False,
            annotate=True,
            cmap='gray'
        )
        
        return fig
    
    def _create_coronal_view(self, nii_img, title):
        """Create coronal view using nilearn"""
        fig = plt.figure(figsize=(12, 8))
        
        # Plot anatomical image
        display = plot_anat(
            nii_img,
            display_mode='y',
            cut_coords=5,
            title=f"{title} - Coronal View",
            figure=fig,
            draw_cross=False,
            annotate=True,
            cmap='gray'
        )
        
        return fig
    
    def _create_axial_overlay(self, mri_img, heatmap_img, title, opacity):
        """Create axial overlay using nilearn"""
        fig = plt.figure(figsize=(12, 8))
        
        # Create statistical map overlay
        display = plot_stat_map(
            heatmap_img,
            bg_img=mri_img,
            display_mode='z',
            cut_coords=5,
            title=f"{title} - Axial Overlay",
            figure=fig,
            draw_cross=False,
            annotate=True,
            cmap='hot',
            alpha=opacity,
            threshold=0.1,  # Only show significant activations
            colorbar=True
        )
        
        return fig
    
    def _create_sagittal_overlay(self, mri_img, heatmap_img, title, opacity):
        """Create sagittal overlay using nilearn"""
        fig = plt.figure(figsize=(12, 8))
        
        # Create statistical map overlay
        display = plot_stat_map(
            heatmap_img,
            bg_img=mri_img,
            display_mode='x',
            cut_coords=5,
            title=f"{title} - Sagittal Overlay",
            figure=fig,
            draw_cross=False,
            annotate=True,
            cmap='hot',
            alpha=opacity,
            threshold=0.1,
            colorbar=True
        )
        
        return fig
    
    def _create_coronal_overlay(self, mri_img, heatmap_img, title, opacity):
        """Create coronal overlay using nilearn"""
        fig = plt.figure(figsize=(12, 8))
        
        # Create statistical map overlay
        display = plot_stat_map(
            heatmap_img,
            bg_img=mri_img,
            display_mode='y',
            cut_coords=5,
            title=f"{title} - Coronal Overlay",
            figure=fig,
            draw_cross=False,
            annotate=True,
            cmap='hot',
            alpha=opacity,
            threshold=0.1,
            colorbar=True
        )
        
        return fig
    
    def save_figure_as_image(self, fig, filename=None, dpi=300):
        """
        Save matplotlib figure as high-resolution image
        
        Args:
            fig: Matplotlib figure
            filename: Output filename (optional)
            dpi: Resolution in DPI
            
        Returns:
            str: Path to saved image or base64 encoded image
        """
        try:
            if filename:
                # Save to file
                fig.savefig(filename, dpi=dpi, bbox_inches='tight', 
                           facecolor='white', edgecolor='none')
                logger.info(f"💾 Figure saved to {filename}")
                return filename
            else:
                # Return as base64 encoded string for web display
                buffer = io.BytesIO()
                fig.savefig(buffer, format='png', dpi=dpi, bbox_inches='tight',
                           facecolor='white', edgecolor='none')
                buffer.seek(0)
                image_base64 = base64.b64encode(buffer.getvalue()).decode()
                buffer.close()
                return f"data:image/png;base64,{image_base64}"
                
        except Exception as e:
            logger.error(f"❌ Failed to save figure: {e}")
            raise
    
    def create_comparison_view(self, mri_data, heatmap_data, affine_matrix, 
                              predicted_class, mmse_score):
        """
        Create side-by-side comparison of MRI and heatmap overlay
        
        Args:
            mri_data: 3D numpy array of MRI data
            heatmap_data: 3D numpy array of heatmap data
            affine_matrix: 4x4 affine transformation matrix
            predicted_class: Predicted class (0=CN, 1=MCI, 2=AD)
            mmse_score: MMSE cognitive score
            
        Returns:
            matplotlib figure: Comparison visualization
        """
        logger.info("📊 Creating comparison view")
        
        try:
            # Create nibabel images
            mri_img = nib.Nifti1Image(mri_data, affine_matrix)
            heatmap_img = nib.Nifti1Image(heatmap_data, affine_matrix)
            
            # Create figure with subplots
            fig, axes = plt.subplots(2, 3, figsize=(18, 12))
            fig.suptitle(f'MRI Analysis - Class: {["CN", "MCI", "AD"][predicted_class]}, MMSE: {mmse_score:.1f}', 
                        fontsize=16, fontweight='bold')
            
            # Row 1: Original MRI in different views
            # Axial
            ax = axes[0, 0]
            display = plot_anat(mri_img, display_mode='z', cut_coords=1, 
                               axes=ax, title="Original MRI - Axial", 
                               draw_cross=False, cmap='gray')
            
            # Sagittal
            ax = axes[0, 1]
            display = plot_anat(mri_img, display_mode='x', cut_coords=1, 
                               axes=ax, title="Original MRI - Sagittal", 
                               draw_cross=False, cmap='gray')
            
            # Coronal
            ax = axes[0, 2]
            display = plot_anat(mri_img, display_mode='y', cut_coords=1, 
                               axes=ax, title="Original MRI - Coronal", 
                               draw_cross=False, cmap='gray')
            
            # Row 2: Heatmap overlays
            # Axial overlay
            ax = axes[1, 0]
            display = plot_stat_map(heatmap_img, bg_img=mri_img, 
                                   display_mode='z', cut_coords=1,
                                   axes=ax, title="Heatmap - Axial",
                                   draw_cross=False, cmap='hot',
                                   alpha=0.7, threshold=0.1)
            
            # Sagittal overlay
            ax = axes[1, 1]
            display = plot_stat_map(heatmap_img, bg_img=mri_img, 
                                   display_mode='x', cut_coords=1,
                                   axes=ax, title="Heatmap - Sagittal",
                                   draw_cross=False, cmap='hot',
                                   alpha=0.7, threshold=0.1)
            
            # Coronal overlay
            ax = axes[1, 2]
            display = plot_stat_map(heatmap_img, bg_img=mri_img, 
                                   display_mode='y', cut_coords=1,
                                   axes=ax, title="Heatmap - Coronal",
                                   draw_cross=False, cmap='hot',
                                   alpha=0.7, threshold=0.1)
            
            plt.tight_layout()
            
            logger.info("✅ Comparison view created successfully")
            return fig
            
        except Exception as e:
            logger.error(f"❌ Comparison view failed: {e}")
            raise
    
    def cleanup_temp_files(self):
        """Clean up temporary files"""
        for temp_file in self.temp_files:
            try:
                if os.path.exists(temp_file):
                    os.unlink(temp_file)
            except:
                pass
        self.temp_files.clear()
        logger.info("🧹 Temporary files cleaned up")

# Example usage and testing
if __name__ == "__main__":
    # Test the visualization system
    viz_system = NilearnVisualizationSystem()
    
    # Load test data
    test_file = "experiment_25_scans/CASE_01_mri.npy"
    if Path(test_file).exists():
        print("🧪 Testing Nilearn Visualization System")
        
        # Load MRI data
        mri_data = np.load(test_file)
        affine_matrix = np.eye(4)  # Identity matrix for .npy files
        
        print(f"✅ Loaded test MRI: {mri_data.shape}")
        
        # Create basic MRI visualization
        mri_views = viz_system.create_mri_visualization(
            mri_data, affine_matrix, "Test MRI"
        )
        
        print(f"✅ Created {len(mri_views)} MRI views")
        
        # Create dummy heatmap for testing
        heatmap_data = np.random.random(mri_data.shape) * 0.5
        heatmap_data[heatmap_data < 0.4] = 0  # Sparse activation
        
        # Create heatmap overlay
        overlay_views = viz_system.create_heatmap_overlay(
            mri_data, heatmap_data, affine_matrix, "Test Overlay"
        )
        
        print(f"✅ Created {len(overlay_views)} overlay views")
        
        # Save a test image
        test_output = "test_nilearn_visualization.png"
        viz_system.save_figure_as_image(mri_views['axial'], test_output)
        
        print(f"💾 Test visualization saved to {test_output}")
        print("🎉 Nilearn visualization system test successful!")
        
        # Cleanup
        viz_system.cleanup_temp_files()
        
    else:
        print("❌ Test file not found")
