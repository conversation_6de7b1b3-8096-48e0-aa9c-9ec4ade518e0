#!/usr/bin/env python3
"""
Test script to verify the Enhanced MCI Streamlit App is working correctly
"""

import sys
import numpy as np
from pathlib import Path

def test_imports():
    """Test all required imports"""
    print("🔍 Testing imports...")
    
    try:
        import streamlit as st
        print("✅ Streamlit imported successfully")
    except ImportError as e:
        print(f"❌ Streamlit import failed: {e}")
        return False
    
    try:
        import torch
        print(f"✅ PyTorch imported successfully (version: {torch.__version__})")
    except ImportError as e:
        print(f"❌ PyTorch import failed: {e}")
        return False
    
    try:
        import numpy as np
        print(f"✅ NumPy imported successfully (version: {np.__version__})")
    except ImportError as e:
        print(f"❌ NumPy import failed: {e}")
        return False
    
    try:
        import nibabel as nib
        print(f"✅ NiBabel imported successfully (version: {nib.__version__})")
    except ImportError as e:
        print(f"❌ NiBabel import failed: {e}")
        return False
    
    try:
        import plotly
        print(f"✅ Plotly imported successfully (version: {plotly.__version__})")
    except ImportError as e:
        print(f"❌ Plotly import failed: {e}")
        return False
    
    return True

def test_model_files():
    """Test if model files are available"""
    print("\n📁 Testing model files...")
    
    original_model_path = Path('best_real_mci_model.pth')
    gated_model_path = Path('best_full_gated_cnn_model.pth')
    confusion_matrix_path = Path('model_comparison_confusion_matrices.png')
    
    if original_model_path.exists():
        size_mb = original_model_path.stat().st_size / (1024 * 1024)
        print(f"✅ Original CNN model found ({size_mb:.1f} MB)")
    else:
        print("❌ Original CNN model not found")
        return False
    
    if gated_model_path.exists():
        size_mb = gated_model_path.stat().st_size / (1024 * 1024)
        print(f"✅ Gated CNN model found ({size_mb:.1f} MB)")
    else:
        print("❌ Gated CNN model not found")
        return False
    
    if confusion_matrix_path.exists():
        size_kb = confusion_matrix_path.stat().st_size / 1024
        print(f"✅ Confusion matrix image found ({size_kb:.1f} KB)")
    else:
        print("⚠️ Confusion matrix image not found (optional)")
    
    return True

def test_model_loading():
    """Test model loading functionality"""
    print("\n🧠 Testing model loading...")
    
    try:
        import torch
        from enhanced_mci_streamlit_app import RealMCIModel, FullGatedCNNModel
        
        # Test Original CNN
        original_model = RealMCIModel()
        original_path = Path('best_real_mci_model.pth')
        if original_path.exists():
            checkpoint = torch.load(original_path, map_location='cpu')
            original_model.load_state_dict(checkpoint['model_state_dict'])
            print("✅ Original CNN model loaded successfully")
        
        # Test Gated CNN
        gated_model = FullGatedCNNModel()
        gated_path = Path('best_full_gated_cnn_model.pth')
        if gated_path.exists():
            checkpoint = torch.load(gated_path, map_location='cpu')
            gated_model.load_state_dict(checkpoint['model_state_dict'])
            print("✅ Gated CNN model loaded successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Model loading failed: {e}")
        return False

def test_inference_engine():
    """Test inference engine functionality"""
    print("\n⚡ Testing inference engine...")
    
    try:
        from enhanced_mci_streamlit_app import EnhancedMCIInferenceEngine
        
        # Initialize inference engine
        engine = EnhancedMCIInferenceEngine(device='cpu')
        
        # Create dummy MRI data
        dummy_mri = np.random.uniform(0, 1, (91, 109, 91))
        
        # Test preprocessing
        mri_tensor, processed_mri, preprocessing_time = engine.fast_preprocess_mri(dummy_mri)
        print(f"✅ MRI preprocessing successful ({preprocessing_time:.3f}s)")
        
        # Test prediction (if models are loaded)
        if engine.original_model is not None or engine.gated_model is not None:
            results = engine.comprehensive_predict(dummy_mri, patient_age=70)
            print("✅ Comprehensive prediction successful")
            
            # Check results structure
            if 'models' in results:
                for model_name in results['models']:
                    print(f"   📊 {model_name} results available")
        
        return True
        
    except Exception as e:
        print(f"❌ Inference engine test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🧠 Enhanced MCI Streamlit App - Test Suite")
    print("=" * 50)
    
    tests = [
        ("Import Test", test_imports),
        ("Model Files Test", test_model_files),
        ("Model Loading Test", test_model_loading),
        ("Inference Engine Test", test_inference_engine)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 Running {test_name}...")
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} FAILED with exception: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The app is ready to use.")
        print("\n🚀 To run the app:")
        print("   streamlit run enhanced_mci_streamlit_app.py")
        return True
    else:
        print("⚠️ Some tests failed. Please check the issues above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
