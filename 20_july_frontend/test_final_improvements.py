#!/usr/bin/env python3
"""
Test Final Improvements: SHAP Heatmaps, Dynamic Scores, Brain Segmentation
"""

import numpy as np
import time
from final_mci_streamlit_app import FinalMCIInferenceEngine

def test_dynamic_cognitive_scores():
    """Test that cognitive scores change dynamically with different MRI inputs"""
    
    print("🧠 Testing Dynamic Cognitive Scores...")
    
    engine = FinalMCIInferenceEngine()
    
    # Test with different MRI patterns
    test_cases = [
        ("Healthy Brain", np.random.normal(0.6, 0.1, (91, 109, 91))),  # High intensity = healthy
        ("MCI Brain", np.random.normal(0.4, 0.15, (91, 109, 91))),     # Medium intensity = MCI
        ("AD Brain", np.random.normal(0.25, 0.2, (91, 109, 91)))       # Low intensity = AD
    ]
    
    results = []
    
    for case_name, mri_data in test_cases:
        print(f"\n📊 Testing {case_name}...")
        
        # Run prediction multiple times to check variability
        scores = []
        predictions = []
        
        for i in range(3):
            result = engine.comprehensive_predict(mri_data)
            
            for model_name, model_results in result['models'].items():
                score = model_results['cognitive_score']
                pred_class = ['CN', 'MCI', 'AD'][model_results['predicted_class']]
                scores.append(score)
                predictions.append(pred_class)
                
                print(f"   {model_name} Run {i+1}: Score={score:.1f}, Class={pred_class}")
        
        avg_score = np.mean(scores)
        score_range = np.max(scores) - np.min(scores)
        
        results.append({
            'case': case_name,
            'avg_score': avg_score,
            'score_range': score_range,
            'predictions': predictions
        })
        
        print(f"   Average Score: {avg_score:.1f} (Range: {score_range:.1f})")
    
    # Check if scores are appropriately different
    healthy_score = results[0]['avg_score']
    mci_score = results[1]['avg_score']
    ad_score = results[2]['avg_score']
    
    print(f"\n📈 Score Comparison:")
    print(f"   Healthy: {healthy_score:.1f}")
    print(f"   MCI: {mci_score:.1f}")
    print(f"   AD: {ad_score:.1f}")
    
    # Verify proper ordering (healthy > MCI > AD generally)
    if healthy_score > mci_score > ad_score:
        print("✅ Cognitive scores show proper progression!")
        return True
    elif abs(healthy_score - ad_score) > 5.0:  # At least some difference
        print("✅ Cognitive scores show reasonable variation!")
        return True
    else:
        print("❌ Cognitive scores are too similar - may be static")
        return False

def test_brain_region_analysis():
    """Test brain region segmentation and analysis"""
    
    print("\n🧠 Testing Brain Region Analysis...")
    
    engine = FinalMCIInferenceEngine()
    
    # Create test MRI with simulated atrophy
    test_mri = np.random.normal(0.3, 0.15, (91, 109, 91))  # Low intensity = atrophy
    
    result = engine.comprehensive_predict(test_mri)
    
    for model_name, model_results in result['models'].items():
        print(f"\n🤖 {model_name} Brain Analysis:")
        
        if 'brain_regions' in model_results:
            brain_analysis = model_results['brain_regions']
            
            print(f"   Cognitive Score: {model_results['cognitive_score']:.1f}")
            print(f"   Affected Regions: {len(brain_analysis['affected_regions'])}")
            
            if brain_analysis['affected_regions']:
                print("   Regions with atrophy:")
                for region in brain_analysis['affected_regions']:
                    if region in brain_analysis['severity_scores']:
                        severity = brain_analysis['severity_scores'][region]['severity']
                        print(f"     - {region}: Severity {severity:.2f}")
            
            print(f"   Interpretation: {brain_analysis['interpretation'][:100]}...")
            print(f"   Clinical Significance: {len(brain_analysis['clinical_significance'])} findings")
            
            return True
        else:
            print("   ❌ No brain region analysis found")
            return False

def test_heatmap_quality():
    """Test heatmap generation and quality"""
    
    print("\n🔥 Testing Heatmap Quality...")
    
    engine = FinalMCIInferenceEngine()
    
    # Test with different cognitive scores
    test_scores = [28.0, 22.0, 16.0]  # CN, MCI, AD
    
    for score in test_scores:
        print(f"\n📊 Testing heatmap for cognitive score {score:.1f}...")
        
        # Create test MRI
        test_mri = np.random.normal(0.4, 0.15, (91, 109, 91))
        
        result = engine.comprehensive_predict(test_mri)
        
        for model_name, model_results in result['models'].items():
            if 'heatmap' in model_results:
                heatmap = model_results['heatmap']
                
                print(f"   {model_name}:")
                print(f"     Heatmap shape: {heatmap.shape}")
                print(f"     Value range: {heatmap.min():.3f} - {heatmap.max():.3f}")
                print(f"     Non-zero values: {np.count_nonzero(heatmap)}")
                print(f"     Mean activation: {np.mean(heatmap):.3f}")
                
                # Check if heatmap has meaningful values
                if heatmap.max() > 0.1 and np.count_nonzero(heatmap) > 1000:
                    print(f"     ✅ Heatmap quality: Good")
                else:
                    print(f"     ⚠️ Heatmap quality: Low")
            else:
                print(f"   ❌ No heatmap generated for {model_name}")
                return False
    
    return True

def test_bias_correction():
    """Test that model bias toward ALZ has been corrected"""
    
    print("\n⚖️ Testing Bias Correction...")
    
    engine = FinalMCIInferenceEngine()
    
    # Test multiple cases to check class distribution
    class_counts = {'CN': 0, 'MCI': 0, 'AD': 0}
    
    for i in range(10):
        # Create varied MRI data
        if i < 4:  # Healthy-looking
            mri_data = np.random.normal(0.6, 0.1, (91, 109, 91))
        elif i < 7:  # Moderate
            mri_data = np.random.normal(0.4, 0.15, (91, 109, 91))
        else:  # Impaired
            mri_data = np.random.normal(0.3, 0.2, (91, 109, 91))
        
        result = engine.comprehensive_predict(mri_data)
        
        for model_name, model_results in result['models'].items():
            pred_class = ['CN', 'MCI', 'AD'][model_results['predicted_class']]
            class_counts[pred_class] += 1
    
    total_predictions = sum(class_counts.values())
    
    print(f"   Class distribution over {total_predictions} predictions:")
    for class_name, count in class_counts.items():
        percentage = (count / total_predictions) * 100
        print(f"     {class_name}: {count} ({percentage:.1f}%)")
    
    # Check if all classes are represented
    if all(count > 0 for count in class_counts.values()):
        print("✅ All classes are being predicted - bias corrected!")
        return True
    else:
        print("❌ Some classes never predicted - bias still present")
        return False

def main():
    """Run all improvement tests"""
    
    print("🧪 TESTING FINAL IMPROVEMENTS")
    print("=" * 50)
    
    tests = [
        ("Dynamic Cognitive Scores", test_dynamic_cognitive_scores),
        ("Brain Region Analysis", test_brain_region_analysis),
        ("Heatmap Quality", test_heatmap_quality),
        ("Bias Correction", test_bias_correction)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            start_time = time.time()
            success = test_func()
            duration = time.time() - start_time
            results[test_name] = {'success': success, 'duration': duration}
            print(f"⏱️ Completed in {duration:.2f}s")
        except Exception as e:
            print(f"❌ Test failed with error: {e}")
            results[test_name] = {'success': False, 'duration': 0}
    
    # Summary
    print(f"\n{'='*20} SUMMARY {'='*20}")
    
    passed = sum(1 for r in results.values() if r['success'])
    total = len(results)
    
    for test_name, result in results.items():
        status = "✅ PASS" if result['success'] else "❌ FAIL"
        print(f"{test_name}: {status} ({result['duration']:.2f}s)")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("\n🎉 ALL IMPROVEMENTS WORKING!")
        print("✅ Dynamic cognitive scores")
        print("✅ SHAP-based heatmaps") 
        print("✅ Brain region segmentation")
        print("✅ Bias correction")
        print("🚀 Ready for presentation!")
    else:
        print(f"\n⚠️ {total-passed} tests failed - check issues above")
    
    return passed == total

if __name__ == "__main__":
    main()
