#!/usr/bin/env python3
"""
Test Restored Gradient Heatmap System
Verify that the original working gradient heatmaps are back
"""

import numpy as np
from final_mci_streamlit_app import FinalMCIInferenceEngine, create_heatmap_visualization

def test_restored_gradient_heatmaps():
    """Test that the restored gradient heatmap system is working"""
    
    print("🔥 TESTING RESTORED GRADIENT HEATMAP SYSTEM")
    print("=" * 60)
    print("Objective: Verify original working gradient heatmaps are restored")
    print()
    
    # Create inference engine
    engine = FinalMCIInferenceEngine()
    
    # Create test MRI
    test_mri = np.random.normal(0.6, 0.1, (91, 109, 91))
    test_mri = np.clip(test_mri, 0, 1)
    
    print(f"✅ Test MRI created: shape {test_mri.shape}")
    print(f"   MRI range: {test_mri.min():.3f} - {test_mri.max():.3f}")
    
    # Test comprehensive prediction with restored gradients
    try:
        results = engine.comprehensive_predict(test_mri)
        
        print(f"\n✅ Comprehensive prediction successful")
        print(f"📊 Models tested: {len(results['models'])}")
        
        # Check each model's heatmap quality
        for model_name, model_results in results['models'].items():
            heatmap = model_results['heatmap']
            mmse_score = model_results['cognitive_score']
            
            print(f"\n🔍 {model_name}:")
            print(f"   MMSE Score: {mmse_score:.1f}")
            print(f"   Heatmap shape: {heatmap.shape}")
            print(f"   Heatmap range: {heatmap.min():.4f} - {heatmap.max():.4f}")
            print(f"   Heatmap mean: {heatmap.mean():.4f}")
            
            # Check heatmap quality
            nonzero_voxels = np.count_nonzero(heatmap)
            total_voxels = heatmap.size
            activation_percentage = nonzero_voxels / total_voxels * 100
            
            print(f"   Activation: {activation_percentage:.2f}% of brain")
            
            # Quality assessment
            if heatmap.max() > 0.5:
                print(f"   🎉 EXCELLENT: Strong heatmap signal (max={heatmap.max():.3f})")
            elif heatmap.max() > 0.1:
                print(f"   ✅ GOOD: Adequate heatmap signal (max={heatmap.max():.3f})")
            elif heatmap.max() > 0.01:
                print(f"   ⚠️ WEAK: Low heatmap signal (max={heatmap.max():.3f})")
            else:
                print(f"   ❌ POOR: Very weak heatmap signal (max={heatmap.max():.3f})")
            
            # Test visualization with different opacities
            try:
                for opacity in [0.3, 0.6, 0.9]:
                    viz_fig = create_heatmap_visualization(
                        test_mri, heatmap, mmse_score, model_name, opacity=opacity
                    )
                    print(f"   ✅ Visualization created with opacity {opacity}")
                    
            except Exception as e:
                print(f"   ❌ Visualization failed: {e}")
    
    except Exception as e:
        print(f"❌ Comprehensive prediction failed: {e}")

def test_gradient_quality():
    """Test the quality of gradient-based heatmaps"""
    
    print(f"\n🧪 TESTING GRADIENT QUALITY")
    print("=" * 40)
    
    engine = FinalMCIInferenceEngine()
    
    # Test with different cognitive scores
    test_cases = [
        {'mmse': 28, 'expected': 'Minimal but visible gradients'},
        {'mmse': 22, 'expected': 'Moderate gradient strength'},
        {'mmse': 18, 'expected': 'Strong gradient patterns'},
        {'mmse': 12, 'expected': 'Very strong gradients'}
    ]
    
    for case in test_cases:
        print(f"\n🧪 Testing MMSE {case['mmse']} - {case['expected']}")
        
        # Create test MRI
        test_mri = np.random.normal(0.6, 0.1, (91, 109, 91))
        test_mri = np.clip(test_mri, 0, 1)
        
        try:
            # Test gradient heatmap generation
            import torch
            mri_tensor = torch.FloatTensor(test_mri).unsqueeze(0).unsqueeze(0)
            
            heatmap = engine.generate_real_gradient_heatmap(
                None,  # Will use gradient-optimized model
                mri_tensor,
                case['mmse'],
                test_mri.shape
            )
            
            print(f"   ✅ Gradient heatmap generated")
            print(f"   📊 Range: {heatmap.min():.4f} - {heatmap.max():.4f}")
            print(f"   📊 Mean: {heatmap.mean():.4f}")
            print(f"   📊 Std: {heatmap.std():.4f}")
            
            # Check gradient strength
            if heatmap.max() > 0.8:
                print(f"   🔥 EXCELLENT: Very strong gradients!")
            elif heatmap.max() > 0.5:
                print(f"   🎉 GREAT: Strong gradients!")
            elif heatmap.max() > 0.2:
                print(f"   ✅ GOOD: Adequate gradients")
            else:
                print(f"   ⚠️ WEAK: Low gradient strength")
                
        except Exception as e:
            print(f"   ❌ Gradient generation failed: {e}")

def main():
    """Main test function"""
    
    print("🔥 RESTORED GRADIENT HEATMAP SYSTEM TEST")
    print("=" * 70)
    print("Testing that the original working gradient heatmaps are restored")
    print("with proper MRI overlay and opacity control")
    print()
    
    test_restored_gradient_heatmaps()
    test_gradient_quality()
    
    print(f"\n🎉 RESTORED HEATMAP TEST COMPLETE!")
    print(f"Check results above - should see strong gradient heatmaps")
    print(f"Frontend URL: http://0.0.0.0:8503")
    print(f"Features:")
    print(f"  • Original gradient-based heatmaps restored")
    print(f"  • MRI overlay visualization")
    print(f"  • Opacity control slider (0.0 - 1.0)")
    print(f"  • Multi-view display (axial, coronal, sagittal)")

if __name__ == "__main__":
    main()
