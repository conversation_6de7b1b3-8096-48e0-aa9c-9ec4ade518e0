#!/usr/bin/env python3
"""
Segmentation-Enhanced Model for Precise Atrophy and Lesion Detection
Implements region-specific segmentation for accurate clinical assessment
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from scipy.ndimage import label, gaussian_filter
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class GatedConv3D(nn.Module):
    """Gated 3D Convolution for enhanced feature learning"""
    
    def __init__(self, in_channels, out_channels, kernel_size=3, padding=1):
        super(GatedConv3D, self).__init__()
        
        # Main convolution
        self.conv = nn.Conv3d(in_channels, out_channels, kernel_size, padding=padding)
        
        # Gate convolution
        self.gate_conv = nn.Conv3d(in_channels, out_channels, kernel_size, padding=padding)
        
        # Batch normalization
        self.bn = nn.BatchNorm3d(out_channels)
        
    def forward(self, x):
        # Main features
        main = self.conv(x)
        
        # Gate features
        gate = torch.sigmoid(self.gate_conv(x))
        
        # Gated output
        output = main * gate
        
        return self.bn(output)

class SegmentationHead(nn.Module):
    """Segmentation head for anatomical region identification"""
    
    def __init__(self, in_channels, num_regions=15):
        super(SegmentationHead, self).__init__()
        
        # Segmentation regions:
        # 0: Background, 1: Hippocampus L, 2: Hippocampus R, 3: Temporal L, 4: Temporal R
        # 5: Frontal L, 6: Frontal R, 7: Parietal L, 8: Parietal R, 9: Occipital L, 10: Occipital R
        # 11: Thalamus L, 12: Thalamus R, 13: Basal Ganglia L, 14: Basal Ganglia R
        
        self.segmentation = nn.Sequential(
            nn.Conv3d(in_channels, 128, 3, padding=1),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),
            nn.Conv3d(128, 64, 3, padding=1),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),
            nn.Conv3d(64, num_regions, 1),  # 1x1 conv for segmentation
            nn.Upsample(scale_factor=8, mode='trilinear', align_corners=False)  # Upsample to original size
        )
    
    def forward(self, x):
        return self.segmentation(x)

class AtrophyDetectionHead(nn.Module):
    """Specific atrophy detection for each anatomical region"""
    
    def __init__(self, in_channels, num_regions=15):
        super(AtrophyDetectionHead, self).__init__()
        
        self.region_atrophy = nn.Sequential(
            nn.Conv3d(in_channels, 256, 3, padding=1),
            nn.BatchNorm3d(256),
            nn.ReLU(inplace=True),
            nn.Conv3d(256, 128, 3, padding=1),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),
            nn.Conv3d(128, num_regions, 1),  # Per-region atrophy scores
            nn.Sigmoid()
        )
    
    def forward(self, x):
        return self.region_atrophy(x)

class LesionDetectionHead(nn.Module):
    """Lesion detection head for strategic infarcts and white matter lesions"""
    
    def __init__(self, in_channels):
        super(LesionDetectionHead, self).__init__()
        
        self.lesion_detection = nn.Sequential(
            nn.Conv3d(in_channels, 128, 3, padding=1),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),
            nn.Conv3d(128, 64, 3, padding=1),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),
            nn.Conv3d(64, 3, 1),  # 3 types: normal, strategic infarct, WM lesion
            nn.Upsample(scale_factor=8, mode='trilinear', align_corners=False)
        )
    
    def forward(self, x):
        return self.lesion_detection(x)

class SegmentationEnhancedMCIModel(nn.Module):
    """Enhanced MCI model with segmentation and specific detection capabilities"""
    
    def __init__(self, num_classes=3, num_regions=15):
        super(SegmentationEnhancedMCIModel, self).__init__()
        
        # Gated CNN backbone
        self.features = nn.Sequential(
            # Block 1
            GatedConv3D(1, 32),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            nn.Dropout3d(0.1),
            
            # Block 2
            GatedConv3D(32, 64),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            nn.Dropout3d(0.1),
            
            # Block 3
            GatedConv3D(64, 128),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            nn.Dropout3d(0.2),
            
            # Block 4
            GatedConv3D(128, 256),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool3d((4, 4, 4))
        )
        
        # Feature size for classification
        self.feature_size = 256 * 4 * 4 * 4
        
        # Multi-task heads
        # 1. Classification head
        self.classifier = nn.Sequential(
            nn.Linear(self.feature_size, 512),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )
        
        # 2. Global atrophy head
        self.atrophy_head = nn.Sequential(
            nn.Linear(self.feature_size, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 1),
            nn.Sigmoid()
        )
        
        # 3. Clinical scores head
        self.clinical_head = nn.Sequential(
            nn.Linear(self.feature_size, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 3)  # MTA, GCA, Koedam
        )
        
        # 4. Segmentation head (uses feature maps before pooling)
        self.segmentation_head = SegmentationHead(128, num_regions)
        
        # 5. Region-specific atrophy detection
        self.region_atrophy_head = AtrophyDetectionHead(128, num_regions)
        
        # 6. Lesion detection head
        self.lesion_head = LesionDetectionHead(128)
        
        # Region names for interpretation
        self.region_names = [
            'background', 'hippocampus_L', 'hippocampus_R', 'temporal_L', 'temporal_R',
            'frontal_L', 'frontal_R', 'parietal_L', 'parietal_R', 'occipital_L', 'occipital_R',
            'thalamus_L', 'thalamus_R', 'basal_ganglia_L', 'basal_ganglia_R'
        ]
    
    def forward(self, x):
        # Extract features through backbone
        features = []
        for i, layer in enumerate(self.features):
            x = layer(x)
            if i == 6:  # After block 3, before pooling
                feature_maps = x  # Save for segmentation heads
        
        # Global features for classification
        global_features = x.view(x.size(0), -1)
        
        # Classification outputs
        classification = self.classifier(global_features)
        atrophy = self.atrophy_head(global_features)
        clinical = self.clinical_head(global_features)
        
        # Segmentation outputs (using feature maps before pooling)
        segmentation = self.segmentation_head(feature_maps)
        region_atrophy = self.region_atrophy_head(feature_maps)
        lesion_detection = self.lesion_head(feature_maps)
        
        return {
            'classification': classification,
            'atrophy': atrophy,
            'clinical': clinical,
            'segmentation': segmentation,
            'region_atrophy': region_atrophy,
            'lesion_detection': lesion_detection,
            'features': global_features
        }
    
    def analyze_specific_atrophy(self, segmentation, region_atrophy):
        """Analyze specific atrophy patterns per anatomical region"""
        
        batch_size = segmentation.size(0)
        results = []
        
        for b in range(batch_size):
            seg_map = torch.argmax(segmentation[b], dim=0).cpu().numpy()
            atrophy_map = region_atrophy[b].cpu().numpy()
            
            region_analysis = {}
            
            for region_id, region_name in enumerate(self.region_names):
                if region_id == 0:  # Skip background
                    continue
                
                # Get region mask
                region_mask = (seg_map == region_id)
                
                if np.sum(region_mask) > 0:
                    # Calculate region-specific atrophy
                    region_atrophy_score = np.mean(atrophy_map[region_id][region_mask])
                    region_volume = np.sum(region_mask)
                    
                    # Determine atrophy severity
                    if region_atrophy_score < 0.2:
                        severity = "Normal"
                    elif region_atrophy_score < 0.4:
                        severity = "Mild"
                    elif region_atrophy_score < 0.7:
                        severity = "Moderate"
                    else:
                        severity = "Severe"
                    
                    region_analysis[region_name] = {
                        'atrophy_score': float(region_atrophy_score),
                        'volume': int(region_volume),
                        'severity': severity
                    }
                else:
                    region_analysis[region_name] = {
                        'atrophy_score': 0.0,
                        'volume': 0,
                        'severity': "Not detected"
                    }
            
            results.append(region_analysis)
        
        return results
    
    def detect_lesions(self, lesion_output):
        """Detect and classify lesions"""
        
        batch_size = lesion_output.size(0)
        results = []
        
        for b in range(batch_size):
            lesion_map = torch.softmax(lesion_output[b], dim=0).cpu().numpy()
            
            # Get lesion predictions
            normal_prob = lesion_map[0]
            strategic_infarct_prob = lesion_map[1]
            wm_lesion_prob = lesion_map[2]
            
            # Detect lesions (threshold-based)
            strategic_infarcts = strategic_infarct_prob > 0.5
            wm_lesions = wm_lesion_prob > 0.5
            
            # Count and locate lesions
            strategic_labels, strategic_count = label(strategic_infarcts)
            wm_labels, wm_count = label(wm_lesions)
            
            lesion_analysis = {
                'strategic_infarcts': {
                    'count': int(strategic_count),
                    'total_volume': int(np.sum(strategic_infarcts)),
                    'max_probability': float(np.max(strategic_infarct_prob))
                },
                'white_matter_lesions': {
                    'count': int(wm_count),
                    'total_volume': int(np.sum(wm_lesions)),
                    'max_probability': float(np.max(wm_lesion_prob))
                },
                'lesion_load': float(np.sum(strategic_infarcts) + np.sum(wm_lesions)) / lesion_map.size
            }
            
            results.append(lesion_analysis)
        
        return results

# Training script for segmentation-enhanced model
def train_segmentation_enhanced_model():
    """Training function for the enhanced model"""
    
    # This would be similar to the previous training but with additional loss terms
    # for segmentation, region-specific atrophy, and lesion detection
    
    logger.info("Training segmentation-enhanced model...")
    
    # Multi-task loss function
    def combined_loss(outputs, targets):
        # Classification loss
        cls_loss = F.cross_entropy(outputs['classification'], targets['class'])
        
        # Atrophy loss
        atrophy_loss = F.mse_loss(outputs['atrophy'].squeeze(), targets['atrophy'])
        
        # Clinical scores loss
        clinical_loss = F.mse_loss(outputs['clinical'], targets['clinical'])
        
        # Segmentation loss (if ground truth available)
        if 'segmentation' in targets:
            seg_loss = F.cross_entropy(outputs['segmentation'], targets['segmentation'])
        else:
            seg_loss = 0
        
        # Region atrophy loss (if ground truth available)
        if 'region_atrophy' in targets:
            region_loss = F.mse_loss(outputs['region_atrophy'], targets['region_atrophy'])
        else:
            region_loss = 0
        
        # Lesion detection loss (if ground truth available)
        if 'lesions' in targets:
            lesion_loss = F.cross_entropy(outputs['lesion_detection'], targets['lesions'])
        else:
            lesion_loss = 0
        
        # Combined loss
        total_loss = (cls_loss + 
                     0.5 * atrophy_loss + 
                     0.3 * clinical_loss + 
                     0.4 * seg_loss + 
                     0.3 * region_loss + 
                     0.2 * lesion_loss)
        
        return total_loss
    
    return "Segmentation-enhanced training setup complete"

if __name__ == "__main__":
    # Test model creation
    model = SegmentationEnhancedMCIModel()
    logger.info(f"Segmentation-enhanced model created with {sum(p.numel() for p in model.parameters())} parameters")
    
    # Test forward pass
    dummy_input = torch.randn(1, 1, 91, 109, 91)
    with torch.no_grad():
        outputs = model(dummy_input)
    
    logger.info("Model outputs:")
    for key, value in outputs.items():
        if isinstance(value, torch.Tensor):
            logger.info(f"  {key}: {value.shape}")
    
    # Test specific analysis
    region_analysis = model.analyze_specific_atrophy(outputs['segmentation'], outputs['region_atrophy'])
    lesion_analysis = model.detect_lesions(outputs['lesion_detection'])
    
    logger.info("✅ Segmentation-enhanced model ready for training!")
