#!/usr/bin/env python3
"""
NACC Data Exploration Script for Enhanced MCI Model Training
Explores NACC data structure, creates MRI-patient mapping, and prepares MCI dataset
"""

import os
import sys
import pandas as pd
import numpy as np
from pathlib import Path
import json
import logging
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NACCDataExplorer:
    """Explore and prepare NACC data for enhanced MCI training"""
    
    def __init__(self, project_dir="/projects/illinois/cob/ba/sridhar"):
        self.project_dir = Path(project_dir)
        self.work_dir = self.project_dir / "enhanced_mci_training"
        self.data_summary = {}
        
        # Ensure working directory exists
        self.work_dir.mkdir(exist_ok=True)
        
        logger.info(f"Initialized NACC Data Explorer")
        logger.info(f"Project directory: {self.project_dir}")
        logger.info(f"Working directory: {self.work_dir}")
    
    def explore_directory_structure(self):
        """Explore the overall directory structure"""
        logger.info("🔍 Exploring directory structure...")
        
        # Key directories to explore
        key_dirs = [
            "nacc_data_dump",
            "NACC_tabular_data", 
            "Nacc imaging file",
            "preprocessedT1scans23june",
            "hahmed"
        ]
        
        structure_info = {}
        
        for dir_name in key_dirs:
            dir_path = self.project_dir / dir_name
            
            if dir_path.exists():
                # Count files and subdirectories
                files = list(dir_path.rglob("*"))
                file_count = len([f for f in files if f.is_file()])
                dir_count = len([f for f in files if f.is_dir()])
                
                # Get file extensions
                extensions = {}
                for file in files:
                    if file.is_file():
                        ext = file.suffix.lower()
                        extensions[ext] = extensions.get(ext, 0) + 1
                
                structure_info[dir_name] = {
                    "exists": True,
                    "file_count": file_count,
                    "dir_count": dir_count,
                    "extensions": extensions,
                    "size_mb": self._get_directory_size(dir_path)
                }
                
                logger.info(f"✅ {dir_name}: {file_count} files, {dir_count} dirs")
                
                # Show sample files
                sample_files = [f.name for f in dir_path.iterdir() if f.is_file()][:5]
                if sample_files:
                    logger.info(f"   Sample files: {sample_files}")
                    
            else:
                structure_info[dir_name] = {"exists": False}
                logger.warning(f"❌ {dir_name}: Directory not found")
        
        self.data_summary["directory_structure"] = structure_info
        return structure_info
    
    def explore_nacc_tabular_data(self):
        """Explore NACC tabular data for clinical features"""
        logger.info("📊 Exploring NACC tabular data...")
        
        tabular_dir = self.project_dir / "NACC_tabular_data"
        if not tabular_dir.exists():
            logger.error("NACC_tabular_data directory not found")
            return None
        
        tabular_info = {}
        
        # Find CSV files
        csv_files = list(tabular_dir.rglob("*.csv"))
        logger.info(f"Found {len(csv_files)} CSV files")
        
        for csv_file in csv_files[:10]:  # Limit to first 10 files
            try:
                df = pd.read_csv(csv_file)
                
                file_info = {
                    "shape": df.shape,
                    "columns": list(df.columns),
                    "dtypes": df.dtypes.to_dict(),
                    "missing_data": df.isnull().sum().to_dict()
                }
                
                # Look for key columns for MCI classification
                key_columns = ['NACCID', 'VISITNUM', 'NACCETPR', 'NACCALZD', 'NACCAGE', 
                              'MMSE', 'CDR', 'NACCUDSD', 'NACCMOCA']
                
                found_key_columns = [col for col in key_columns if col in df.columns]
                file_info["key_columns_found"] = found_key_columns
                
                tabular_info[csv_file.name] = file_info
                
                logger.info(f"  📄 {csv_file.name}: {df.shape[0]} rows, {df.shape[1]} columns")
                if found_key_columns:
                    logger.info(f"     Key columns: {found_key_columns}")
                
            except Exception as e:
                logger.error(f"Error reading {csv_file}: {e}")
        
        self.data_summary["tabular_data"] = tabular_info
        return tabular_info
    
    def explore_imaging_files(self):
        """Explore imaging files for MRI-patient mapping"""
        logger.info("🖼️ Exploring imaging files...")
        
        imaging_dir = self.project_dir / "Nacc imaging file"
        if not imaging_dir.exists():
            logger.error("Nacc imaging file directory not found")
            return None
        
        imaging_info = {}
        
        # Look for mapping files
        mapping_files = list(imaging_dir.rglob("*.csv")) + list(imaging_dir.rglob("*.txt"))
        logger.info(f"Found {len(mapping_files)} potential mapping files")
        
        for mapping_file in mapping_files:
            try:
                # Try to read as CSV first
                if mapping_file.suffix.lower() == '.csv':
                    df = pd.read_csv(mapping_file)
                else:
                    # Try tab-separated
                    df = pd.read_csv(mapping_file, sep='\t')
                
                file_info = {
                    "shape": df.shape,
                    "columns": list(df.columns),
                    "sample_data": df.head().to_dict()
                }
                
                imaging_info[mapping_file.name] = file_info
                
                logger.info(f"  📄 {mapping_file.name}: {df.shape[0]} rows, {df.shape[1]} columns")
                logger.info(f"     Columns: {list(df.columns)}")
                
            except Exception as e:
                logger.error(f"Error reading {mapping_file}: {e}")
        
        self.data_summary["imaging_files"] = imaging_info
        return imaging_info
    
    def explore_preprocessed_scans(self):
        """Explore preprocessed T1 scans"""
        logger.info("🧠 Exploring preprocessed T1 scans...")
        
        preprocessed_dir = self.project_dir / "preprocessedT1scans23june"
        if not preprocessed_dir.exists():
            logger.error("preprocessedT1scans23june directory not found")
            return None
        
        scan_info = {}
        
        # Find MRI files
        mri_extensions = ['.npy', '.nii', '.nii.gz']
        mri_files = []
        
        for ext in mri_extensions:
            mri_files.extend(list(preprocessed_dir.rglob(f"*{ext}")))
        
        logger.info(f"Found {len(mri_files)} MRI files")
        
        # Sample a few files to check format and size
        sample_files = mri_files[:5]
        
        for mri_file in sample_files:
            try:
                if mri_file.suffix == '.npy':
                    data = np.load(mri_file)
                    file_info = {
                        "format": "numpy",
                        "shape": data.shape,
                        "dtype": str(data.dtype),
                        "size_mb": mri_file.stat().st_size / (1024*1024),
                        "data_range": [float(data.min()), float(data.max())]
                    }
                else:
                    # For NIfTI files, just get basic info
                    file_info = {
                        "format": "nifti",
                        "size_mb": mri_file.stat().st_size / (1024*1024)
                    }
                
                scan_info[mri_file.name] = file_info
                
                logger.info(f"  🧠 {mri_file.name}: {file_info}")
                
            except Exception as e:
                logger.error(f"Error reading {mri_file}: {e}")
        
        # Overall statistics
        total_size_gb = sum([f.stat().st_size for f in mri_files]) / (1024**3)
        
        scan_summary = {
            "total_files": len(mri_files),
            "total_size_gb": total_size_gb,
            "file_extensions": {ext: len([f for f in mri_files if f.suffix == ext]) 
                              for ext in mri_extensions},
            "sample_files": scan_info
        }
        
        self.data_summary["preprocessed_scans"] = scan_summary
        logger.info(f"📊 Preprocessed scans summary: {len(mri_files)} files, {total_size_gb:.2f} GB")
        
        return scan_summary
    
    def create_mci_classification_strategy(self):
        """Create strategy for MCI classification from NACC data"""
        logger.info("🎯 Creating MCI classification strategy...")
        
        strategy = {
            "classification_approach": "continuous_atrophy_scoring",
            "label_mapping": {
                0: "Cognitive Normal (CN)",
                1: "Mild Cognitive Impairment (MCI)", 
                2: "Alzheimer's Disease (AD)"
            },
            "key_features": [
                "NACCETPR",  # Etiologic diagnosis
                "NACCALZD",  # Alzheimer's disease diagnosis
                "CDR",       # Clinical Dementia Rating
                "MMSE",      # Mini-Mental State Exam
                "NACCMOCA",  # Montreal Cognitive Assessment
                "NACCAGE"    # Age
            ],
            "classification_rules": {
                "CN": "NACCETPR == 0 and CDR <= 0.5 and MMSE >= 24",
                "MCI": "NACCETPR in [1,2] or (CDR == 0.5 and MMSE >= 20)",
                "AD": "NACCETPR == 3 or NACCALZD == 1 or CDR >= 1.0"
            },
            "continuous_scoring": {
                "formula": "weighted_combination(CDR, MMSE_normalized, age_factor)",
                "range": "[0.0, 1.0]",
                "thresholds": {
                    "CN_MCI": 0.33,
                    "MCI_AD": 0.67
                }
            }
        }
        
        self.data_summary["mci_strategy"] = strategy
        return strategy
    
    def _get_directory_size(self, directory):
        """Get directory size in MB"""
        try:
            total_size = sum(f.stat().st_size for f in directory.rglob('*') if f.is_file())
            return total_size / (1024 * 1024)  # Convert to MB
        except:
            return 0
    
    def save_exploration_report(self):
        """Save exploration report to file"""
        report_file = self.work_dir / f"nacc_data_exploration_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(report_file, 'w') as f:
            json.dump(self.data_summary, f, indent=2, default=str)
        
        logger.info(f"📄 Exploration report saved: {report_file}")
        
        # Also create a human-readable summary
        summary_file = self.work_dir / "data_exploration_summary.txt"
        with open(summary_file, 'w') as f:
            f.write("NACC Data Exploration Summary\n")
            f.write("=" * 50 + "\n\n")
            
            for key, value in self.data_summary.items():
                f.write(f"{key.upper()}:\n")
                f.write(f"{value}\n\n")
        
        logger.info(f"📄 Summary report saved: {summary_file}")
        
        return report_file, summary_file

def main():
    """Main execution function"""
    logger.info("🚀 Starting NACC data exploration...")
    
    # Initialize explorer
    explorer = NACCDataExplorer()
    
    # Run exploration steps
    try:
        explorer.explore_directory_structure()
        explorer.explore_nacc_tabular_data()
        explorer.explore_imaging_files()
        explorer.explore_preprocessed_scans()
        explorer.create_mci_classification_strategy()
        
        # Save reports
        report_file, summary_file = explorer.save_exploration_report()
        
        logger.info("✅ NACC data exploration completed successfully!")
        logger.info(f"📊 Check reports: {report_file} and {summary_file}")
        
    except Exception as e:
        logger.error(f"❌ Exploration failed: {e}")
        raise

if __name__ == "__main__":
    main()
