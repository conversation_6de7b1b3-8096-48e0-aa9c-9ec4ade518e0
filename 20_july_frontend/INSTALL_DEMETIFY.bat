@echo off
setlocal enabledelayedexpansion

REM Demetify NCOMMS2022 - Complete Windows Standalone Installer
REM This script installs Python, Conda, and all dependencies automatically

echo 🧠 Demetify NCOMMS2022 - Windows Standalone Installer
echo ======================================================
echo This will install Python, Conda, and all dependencies automatically.
echo Installation may take 10-15 minutes depending on your internet connection.
echo.

REM Check if running as administrator
net session >nul 2>&1
if %errorLevel% == 0 (
    echo ✅ Running as Administrator
) else (
    echo ⚠️  Not running as Administrator - some features may not work optimally
)

echo.
echo Starting installation process...
echo.

REM Function to check if Miniconda is installed
:check_conda
echo [INFO] Checking for Conda installation...

where conda >nul 2>nul
if %ERRORLEVEL% EQU 0 (
    echo ✅ Conda already installed
    goto setup_environment
)

REM Check common installation paths
if exist "%USERPROFILE%\miniconda3\Scripts\conda.exe" (
    echo ✅ Found Miniconda in user directory
    set "CONDA_PATH=%USERPROFILE%\miniconda3"
    goto setup_environment
)

if exist "C:\ProgramData\Miniconda3\Scripts\conda.exe" (
    echo ✅ Found Miniconda in system directory
    set "CONDA_PATH=C:\ProgramData\Miniconda3"
    goto setup_environment
)

echo [INFO] Conda not found. Installing Miniconda...
goto install_miniconda

:install_miniconda
echo [INFO] Downloading Miniconda installer...

REM Create temp directory
set "TEMP_DIR=%TEMP%\demetify_install"
if not exist "%TEMP_DIR%" mkdir "%TEMP_DIR%"
cd /d "%TEMP_DIR%"

REM Download Miniconda installer
set "MINICONDA_URL=https://repo.anaconda.com/miniconda/Miniconda3-latest-Windows-x86_64.exe"
set "INSTALLER_NAME=Miniconda3-latest-Windows-x86_64.exe"

echo Downloading from: %MINICONDA_URL%
powershell -Command "Invoke-WebRequest -Uri '%MINICONDA_URL%' -OutFile '%INSTALLER_NAME%'"

if not exist "%INSTALLER_NAME%" (
    echo ❌ Failed to download Miniconda installer
    echo Please download manually from: https://docs.conda.io/en/latest/miniconda.html
    pause
    exit /b 1
)

echo [INFO] Installing Miniconda...
echo This may take a few minutes...

REM Install Miniconda silently
"%INSTALLER_NAME%" /InstallationType=JustMe /RegisterPython=1 /S /D=%USERPROFILE%\miniconda3

REM Wait for installation to complete
timeout /t 30 /nobreak >nul

REM Clean up
cd /d "%~dp0"
rmdir /s /q "%TEMP_DIR%"

REM Set conda path
set "CONDA_PATH=%USERPROFILE%\miniconda3"

echo ✅ Miniconda installed successfully

:setup_environment
echo [INFO] Setting up Demetify environment...

REM Initialize conda for this session
if defined CONDA_PATH (
    call "%CONDA_PATH%\Scripts\activate.bat"
) else (
    call conda activate base
)

REM Create demetify environment
echo [INFO] Creating 'demetify' conda environment...
conda create -n demetify python=3.9 -y

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to create conda environment
    pause
    exit /b 1
)

REM Activate the environment
call conda activate demetify

echo ✅ Environment created and activated

:install_dependencies
echo [INFO] Installing Python dependencies...

REM Install PyTorch
echo [INFO] Installing PyTorch...
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y
if %ERRORLEVEL% NEQ 0 (
    echo [WARNING] CUDA version failed, installing CPU version...
    conda install pytorch torchvision torchaudio cpuonly -c pytorch -y
)

REM Install other dependencies
echo [INFO] Installing other dependencies...
pip install streamlit>=1.28.0
pip install numpy>=1.24.0
pip install pandas>=2.0.0
pip install matplotlib>=3.7.0
pip install scikit-learn>=1.3.0
pip install scipy>=1.11.0
pip install nibabel>=5.1.0
pip install nilearn>=0.10.0
pip install shap>=0.41.0
pip install tqdm>=4.65.0
pip install Pillow>=10.0.0

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Failed to install some dependencies
    echo The system may still work, but some features might be limited
)

echo ✅ Dependencies installed

:create_launchers
echo [INFO] Creating launcher scripts...

REM Create batch launcher
echo @echo off > launch_demetify.bat
echo echo 🧠 Starting Demetify NCOMMS2022... >> launch_demetify.bat
echo. >> launch_demetify.bat
echo REM Find conda installation >> launch_demetify.bat
echo if exist "%%USERPROFILE%%\miniconda3\Scripts\activate.bat" ^( >> launch_demetify.bat
echo     call "%%USERPROFILE%%\miniconda3\Scripts\activate.bat" demetify >> launch_demetify.bat
echo ^) else if exist "%%USERPROFILE%%\anaconda3\Scripts\activate.bat" ^( >> launch_demetify.bat
echo     call "%%USERPROFILE%%\anaconda3\Scripts\activate.bat" demetify >> launch_demetify.bat
echo ^) else if exist "C:\ProgramData\Miniconda3\Scripts\activate.bat" ^( >> launch_demetify.bat
echo     call "C:\ProgramData\Miniconda3\Scripts\activate.bat" demetify >> launch_demetify.bat
echo ^) else ^( >> launch_demetify.bat
echo     echo ❌ Conda not found. Please run INSTALL_DEMETIFY.bat first. >> launch_demetify.bat
echo     pause >> launch_demetify.bat
echo     exit /b 1 >> launch_demetify.bat
echo ^) >> launch_demetify.bat
echo. >> launch_demetify.bat
echo echo ✅ Environment activated >> launch_demetify.bat
echo echo 🚀 Launching Demetify frontend... >> launch_demetify.bat
echo echo    Access at: http://localhost:8501 >> launch_demetify.bat
echo echo    Press Ctrl+C to stop >> launch_demetify.bat
echo echo. >> launch_demetify.bat
echo. >> launch_demetify.bat
echo REM Launch Streamlit >> launch_demetify.bat
echo streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address 0.0.0.0 >> launch_demetify.bat
echo. >> launch_demetify.bat
echo pause >> launch_demetify.bat

echo ✅ Launcher created

:run_tests
echo [INFO] Running system tests...

call conda activate demetify
python test_ncomms2022_system.py

if %ERRORLEVEL% EQU 0 (
    echo ✅ All tests passed!
) else (
    echo ⚠️  Some tests failed, but the system should still work
)

:completion
echo.
echo ✅ 🎉 Installation completed successfully!
echo.
echo To launch Demetify:
echo   Double-click: launch_demetify.bat
echo.
echo Or manually:
echo   1. Open Command Prompt
echo   2. conda activate demetify
echo   3. streamlit run demetify_ncomms2022_app.py
echo.
echo ✅ Demetify is ready to use!
echo.

pause
exit /b 0
