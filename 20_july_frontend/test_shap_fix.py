"""
Quick test to verify SHAP interpretability fixes
"""

import numpy as np
import time
import logging

# Import our components
from ncomms2022_model_enhanced import NCOMMSClassifier
from ncomms2022_shap import NCOMMSSHAPExplainer

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_shap_speed():
    """Test SHAP interpretability speed and functionality"""
    print("🔍 Testing SHAP Interpretability Fixes")
    print("=" * 50)
    
    try:
        # Initialize components
        print("1. Initializing classifier...")
        classifier = NCOMMSClassifier()
        
        print("2. Initializing SHAP explainer...")
        explainer = NCOMMSSHAPExplainer(classifier)
        
        # Load test data
        demo_file = "ncomms2022_original/demo/mri/demo1.npy"
        print(f"3. Loading test data: {demo_file}")
        mri_data = np.load(demo_file)
        print(f"   Data shape: {mri_data.shape}")
        
        # Test gradient explanation directly
        print("4. Testing gradient-based explanation...")
        start_time = time.time()
        
        add_shap = explainer._gradient_explanation(mri_data, 'ADD')
        
        gradient_time = time.time() - start_time
        print(f"   ✓ Gradient explanation completed in {gradient_time:.2f} seconds")
        print(f"   ✓ SHAP shape: {add_shap.shape}")
        print(f"   ✓ SHAP range: [{add_shap.min():.6f}, {add_shap.max():.6f}]")
        print(f"   ✓ Non-zero values: {np.count_nonzero(add_shap)}")
        
        # Test visualization generation
        print("5. Testing visualization generation...")
        start_time = time.time()
        
        visualizations = explainer.generate_slice_visualizations(mri_data, add_shap)
        
        viz_time = time.time() - start_time
        print(f"   ✓ Visualizations completed in {viz_time:.2f} seconds")
        print(f"   ✓ Generated views: {list(visualizations.keys())}")
        
        # Check visualization content
        for view_name, view_data in visualizations.items():
            print(f"   ✓ {view_name}: MRI {view_data['mri'].shape}, Overlay {view_data['overlay'].shape}, Heatmap {view_data['heatmap'].shape}")
            print(f"      - Heatmap active pixels: {np.count_nonzero(view_data['heatmap'])}")
        
        total_time = gradient_time + viz_time
        print(f"\n✅ Total interpretability time: {total_time:.2f} seconds")
        
        if total_time < 10:
            print("🎉 SHAP interpretability is now fast enough for real-time use!")
        else:
            print("⚠️  Still slow, but functional")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_with_different_data():
    """Test with different data sizes"""
    print("\n6. Testing with radiologist cohort data...")
    
    try:
        classifier = NCOMMSClassifier()
        explainer = NCOMMSSHAPExplainer(classifier)
        
        test_file = "/mnt/z/radiologist_test_cohort_25/T1_ALZHEIMERS_demo_case1.npy"
        if not os.path.exists(test_file):
            print("   ⚠️  Radiologist cohort file not found, skipping")
            return True
        
        mri_data = np.load(test_file)
        print(f"   Data shape: {mri_data.shape}")
        
        start_time = time.time()
        add_shap = explainer._gradient_explanation(mri_data, 'ADD')
        visualizations = explainer.generate_slice_visualizations(mri_data, add_shap)
        total_time = time.time() - start_time
        
        print(f"   ✓ Completed in {total_time:.2f} seconds")
        print(f"   ✓ Generated {len(visualizations)} views")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Failed: {e}")
        return False

if __name__ == "__main__":
    import os
    
    success1 = test_shap_speed()
    success2 = test_with_different_data()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("🎉 All SHAP tests passed! Frontend should now work properly.")
        print("\n📋 Summary of fixes:")
        print("✓ Added progress indicators and timeout handling")
        print("✓ Improved gradient-based explanations with smoothing")
        print("✓ Better error handling and fallback visualizations")
        print("✓ Robust slice extraction with bounds checking")
        print("✓ Enhanced overlay creation with proper normalization")
    else:
        print("⚠️  Some tests failed. Check the errors above.")
