#!/usr/bin/env python3
"""
Find MRI scans that actually have NACCETPR labels
"""

import os
import pandas as pd
import re
from pathlib import Path

def load_naccetpr_data():
    """Load NACCETPR data and get all available NACC IDs."""
    csv_path = "ncomms2022/FigureTable/NeuroPathTable/nacc_np.csv"
    
    try:
        df = pd.read_csv(csv_path)
        print(f"Loaded NACCETPR data: {len(df)} records")
        
        # Create mapping and show available IDs
        naccetpr_map = {}
        available_ids = []
        
        for _, row in df.iterrows():
            naccid = str(row['NACCID']).zfill(6)  # Pad with zeros
            naccetpr = row['NACCETPR']
            naccetpr_map[naccid] = naccetpr
            available_ids.append(naccid)
        
        print(f"Available NACC IDs: {len(available_ids)}")
        print(f"Sample IDs: {available_ids[:10]}")
        
        return naccetpr_map, available_ids
    
    except Exception as e:
        print(f"Error loading NACCETPR data: {e}")
        return {}, []

def extract_all_nacc_patterns(filename):
    """Extract all possible NACC ID patterns from filename."""
    patterns = [
        r'NACC(\d+)',           # NACC followed by numbers
        r'nacc(\d+)',           # lowercase nacc
        r'(\d{6})',             # 6-digit numbers (common NACC format)
        r'(\d{4,8})',           # 4-8 digit numbers
    ]
    
    found_ids = []
    for pattern in patterns:
        matches = re.findall(pattern, filename, re.IGNORECASE)
        for match in matches:
            # Try different padding
            found_ids.extend([
                match,
                match.zfill(6),
                match.zfill(4),
                match.lstrip('0') if len(match) > 1 else match
            ])
    
    return list(set(found_ids))  # Remove duplicates

def search_for_labeled_files():
    """Search for files that have matching NACCETPR labels."""
    
    print("🔍 Searching for MRI files with NACCETPR labels")
    print("=" * 60)
    
    # Load NACCETPR data
    naccetpr_map, available_ids = load_naccetpr_data()
    
    if not naccetpr_map:
        print("❌ No NACCETPR data loaded")
        return []
    
    # Search directories
    search_dirs = [
        "/mnt/e/MRI_scans_to_examine/",
        "/mnt/e/test_processed/",
        "/mnt/e/processed_data/",
    ]
    
    labeled_files = []
    
    for search_dir in search_dirs:
        if not os.path.exists(search_dir):
            print(f"⚠️ Directory not found: {search_dir}")
            continue
        
        print(f"\n📁 Searching: {search_dir}")
        
        # Get all files
        all_files = []
        for root, dirs, files in os.walk(search_dir):
            for file in files:
                if file.endswith(('.nii', '.npy')):
                    all_files.append(os.path.join(root, file))
        
        print(f"Found {len(all_files)} MRI files")
        
        # Check each file for NACC ID matches
        for i, filepath in enumerate(all_files):
            if i % 100 == 0:
                print(f"  Checking {i+1}/{len(all_files)}...")
            
            filename = os.path.basename(filepath)
            possible_ids = extract_all_nacc_patterns(filename)
            
            for nacc_id in possible_ids:
                if nacc_id in naccetpr_map:
                    naccetpr = naccetpr_map[nacc_id]
                    labeled_files.append({
                        'filepath': filepath,
                        'filename': filename,
                        'nacc_id': nacc_id,
                        'naccetpr': naccetpr,
                        'file_size_mb': round(os.path.getsize(filepath) / (1024*1024), 2)
                    })
                    print(f"  ✅ MATCH: {filename}")
                    print(f"     NACC ID: {nacc_id}, NACCETPR: {naccetpr}")
                    break
    
    return labeled_files

def create_labeled_collection():
    """Create collection with only labeled files."""
    
    print("\n🏷️ Creating Collection with NACCETPR Labels Only")
    print("=" * 60)
    
    # Find labeled files
    labeled_files = search_for_labeled_files()
    
    if not labeled_files:
        print("❌ No files with NACCETPR labels found!")
        print("Let me show you the available NACC IDs to help debug:")
        
        naccetpr_map, available_ids = load_naccetpr_data()
        print(f"\nAvailable NACC IDs in database:")
        for i, nacc_id in enumerate(available_ids[:20]):
            naccetpr = naccetpr_map[nacc_id]
            print(f"  {nacc_id}: NACCETPR={naccetpr}")
        
        return None
    
    print(f"\n🎉 Found {len(labeled_files)} files with NACCETPR labels!")
    
    # Group by NACCETPR value
    by_label = {}
    for file_info in labeled_files:
        naccetpr = file_info['naccetpr']
        if naccetpr not in by_label:
            by_label[naccetpr] = []
        by_label[naccetpr].append(file_info)
    
    print(f"\n📊 Distribution by NACCETPR:")
    for naccetpr, files in sorted(by_label.items()):
        label_name = {0: "NORMAL", 1: "ALZHEIMERS", 2: "OTHER_DEMENTIA"}.get(naccetpr, f"UNKNOWN_{naccetpr}")
        print(f"  NACCETPR {naccetpr} ({label_name}): {len(files)} files")
    
    return labeled_files, by_label

if __name__ == "__main__":
    result = create_labeled_collection()
    if result:
        labeled_files, by_label = result
        print(f"\n✅ Ready to create collection with {len(labeled_files)} labeled files")
    else:
        print("\n❌ No labeled files found - will use demo files only")
