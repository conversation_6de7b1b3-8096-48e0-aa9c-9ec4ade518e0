#!/usr/bin/env python3
"""
<PERSON>ript to update the sample loading in streamlit_demo.py to use real samples
"""

import re

def update_sample_loading():
    """Update the sample loading function to use real demo samples"""
    
    # Read the current file
    with open('streamlit_demo.py', 'r') as f:
        content = f.read()
    
    # Define the new sample loading code
    new_sample_code = '''@st.cache_data
def load_sample_data():
    """Load sample MRI data"""
    samples = {}
    sample_dir = "demo_samples"
    
    # Load actual sample info from the demo package
    try:
        with open(os.path.join(sample_dir, "samples_info.json"), 'r') as f:
            sample_info_data = json.load(f)
        
        # Map the samples to our expected format
        sample_mapping = {
            "normal_1": {
                "file": "sample_1_Normal_Controls.npy",
                "description": "Normal control - cognitively healthy individual",
                "expected": "Normal Controls"
            },
            "normal_2": {
                "file": "sample_2_Normal_Controls.npy",
                "description": "Normal control - cognitively healthy individual", 
                "expected": "Normal Controls"
            },
            "alzheimers": {
                "file": "sample_3_Alzheimers_Disease.npy",
                "description": "Patient with Alzheimer's Disease diagnosis",
                "expected": "Alzheimer's Disease"
            },
            "normal_3": {
                "file": "sample_4_Normal_Controls.npy",
                "description": "Normal control - cognitively healthy individual",
                "expected": "Normal Controls"
            }
        }
        
    except Exception as e:
        st.warning(f"Could not load samples_info.json: {e}")
        # Fallback to default mapping
        sample_mapping = {
            "normal_1": {
                "file": "sample_1_Normal_Controls.npy",
                "description": "Normal control - cognitively healthy individual",
                "expected": "Normal Controls"
            },
            "alzheimers": {
                "file": "sample_3_Alzheimers_Disease.npy",
                "description": "Patient with Alzheimer's Disease diagnosis",
                "expected": "Alzheimer's Disease"
            }
        }
    
    # Try to load actual sample files
    for key, info in sample_mapping.items():
        file_path = os.path.join(sample_dir, info["file"])
        if os.path.exists(file_path):
            try:
                data = np.load(file_path)
                samples[key] = {
                    "data": data,
                    "description": info["description"],
                    "expected": info["expected"]
                }
                print(f"✓ Loaded {info['file']}: shape {data.shape}, dtype {data.dtype}")
            except Exception as e:
                st.warning(f"Could not load {info['file']}: {e}")
                # Create dummy data as fallback
                samples[key] = {
                    "data": create_dummy_mri_data(),
                    "description": info["description"] + " (Demo Data)",
                    "expected": info["expected"]
                }
        else:
            # Create dummy data for demo
            samples[key] = {
                "data": create_dummy_mri_data(),
                "description": info["description"] + " (Demo Data)",
                "expected": info["expected"]
            }
    
    return samples'''
    
    # Find and replace the load_sample_data function
    pattern = r'@st\.cache_data\s*\ndef load_sample_data\(\):.*?return samples'
    
    # Use DOTALL flag to match across newlines
    new_content = re.sub(pattern, new_sample_code, content, flags=re.DOTALL)
    
    # Write the updated content back
    with open('streamlit_demo.py', 'w') as f:
        f.write(new_content)
    
    print("✅ Updated sample loading function to use real demo samples")

if __name__ == "__main__":
    update_sample_loading()
