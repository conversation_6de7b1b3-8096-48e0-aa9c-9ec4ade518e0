#!/usr/bin/env python3
"""
Basic Streamlit test to ensure environment works
"""

import streamlit as st
import numpy as np

# Page configuration
st.set_page_config(
    page_title="🧠 Basic Test",
    page_icon="🧠",
    layout="wide"
)

def main():
    st.title("🧠 Basic Streamlit Test")
    st.write("This is a basic test to ensure Streamlit is working.")
    
    st.success("✅ Streamlit is working!")
    
    # Test basic functionality
    st.subheader("Basic Functionality Test")
    
    # Test metrics
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("Test 1", "Working")
    with col2:
        st.metric("Test 2", "Working")
    with col3:
        st.metric("Test 3", "Working")
    
    # Test file uploader
    st.subheader("File Upload Test")
    uploaded_file = st.file_uploader("Upload a test file", type=['nii', 'npy', 'txt'])
    
    if uploaded_file is not None:
        st.success(f"✅ File uploaded: {uploaded_file.name}")
        st.write(f"File size: {len(uploaded_file.getvalue())} bytes")
    
    # Test numpy
    st.subheader("NumPy Test")
    try:
        arr = np.random.rand(5, 5)
        st.write("Random 5x5 array:")
        st.write(arr)
        st.success("✅ NumPy is working!")
    except Exception as e:
        st.error(f"❌ NumPy error: {e}")
    
    # Test button
    if st.button("Test Button"):
        st.balloons()
        st.success("✅ Button clicked!")

if __name__ == "__main__":
    main()
