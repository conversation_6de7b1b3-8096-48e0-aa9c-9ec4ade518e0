#!/usr/bin/env python3
"""
Diagnose MRI loading and visualization issues
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import nibabel as nib

def diagnose_mri_loading():
    """Diagnose what's wrong with MRI loading and visualization"""
    
    print("🔍 DIAGNOSING MRI LOADING AND VISUALIZATION ISSUES")
    print("=" * 60)
    
    # Test cases
    test_files = [
        "experiment_25_scans/CASE_01_mri.npy",
        "experiment_25_scans/CASE_12_mri.npy", 
        "experiment_25_scans/CASE_18_mri.npy"
    ]
    
    for i, test_file in enumerate(test_files):
        if not Path(test_file).exists():
            print(f"❌ {test_file} not found")
            continue
            
        print(f"\n{i+1}. Analyzing {test_file}...")
        
        try:
            # Load raw MRI data
            mri_data = np.load(test_file)
            print(f"   📊 Raw shape: {mri_data.shape}")
            print(f"   📊 Data type: {mri_data.dtype}")
            print(f"   📊 Value range: {np.min(mri_data):.3f} to {np.max(mri_data):.3f}")
            print(f"   📊 Mean: {np.mean(mri_data):.3f}, Std: {np.std(mri_data):.3f}")
            
            # Check for common issues
            if np.all(mri_data == 0):
                print("   ❌ ERROR: All values are zero!")
                continue
                
            if np.isnan(mri_data).any():
                print("   ⚠️ WARNING: Contains NaN values")
                
            if np.isinf(mri_data).any():
                print("   ⚠️ WARNING: Contains infinite values")
            
            # Check data distribution
            non_zero_pct = np.sum(mri_data != 0) / mri_data.size * 100
            print(f"   📊 Non-zero voxels: {non_zero_pct:.1f}%")
            
            # Create basic visualization to check if data looks like brain
            fig, axes = plt.subplots(1, 3, figsize=(15, 5))
            
            # Get middle slices
            mid_x = mri_data.shape[0] // 2
            mid_y = mri_data.shape[1] // 2  
            mid_z = mri_data.shape[2] // 2
            
            # Axial slice
            axes[0].imshow(mri_data[:, :, mid_z], cmap='gray')
            axes[0].set_title(f'Axial (z={mid_z})')
            axes[0].axis('off')
            
            # Sagittal slice
            axes[1].imshow(mri_data[mid_x, :, :], cmap='gray')
            axes[1].set_title(f'Sagittal (x={mid_x})')
            axes[1].axis('off')
            
            # Coronal slice
            axes[2].imshow(mri_data[:, mid_y, :], cmap='gray')
            axes[2].set_title(f'Coronal (y={mid_y})')
            axes[2].axis('off')
            
            plt.tight_layout()
            
            # Save diagnostic image
            output_file = f"mri_diagnosis_{Path(test_file).stem}.png"
            plt.savefig(output_file, dpi=150, bbox_inches='tight')
            plt.close()
            
            print(f"   💾 Diagnostic image saved: {output_file}")
            
            # Check if it looks like a brain
            brain_like_score = 0
            
            # Check 1: Reasonable intensity distribution
            if 0.1 < np.std(mri_data) < 2.0:
                brain_like_score += 1
                
            # Check 2: Not too sparse
            if non_zero_pct > 20:
                brain_like_score += 1
                
            # Check 3: Has structure (not random noise)
            grad_x = np.gradient(mri_data, axis=0)
            grad_magnitude = np.sqrt(np.sum(grad_x**2))
            if 0.1 < grad_magnitude < 10:
                brain_like_score += 1
            
            if brain_like_score >= 2:
                print("   ✅ Data appears to be valid brain MRI")
            else:
                print("   ⚠️ Data may not be proper brain MRI")
                
        except Exception as e:
            print(f"   ❌ Error loading {test_file}: {e}")
    
    print("\n" + "=" * 60)
    print("🔍 CHECKING EXISTING FRONTENDS")
    print("=" * 60)
    
    # Check what frontends we have
    frontend_files = [
        "neurologist_ready_frontend.py",
        "demetify_final_enhanced/stable_real_demo.py",
        "demetify_final_enhanced/streamlit_app.py",
        "demo_app/streamlit_app.py"
    ]
    
    working_frontends = []
    
    for frontend in frontend_files:
        if Path(frontend).exists():
            print(f"✅ Found: {frontend}")
            working_frontends.append(frontend)
            
            # Check file size and modification time
            file_path = Path(frontend)
            size_kb = file_path.stat().st_size / 1024
            print(f"   📊 Size: {size_kb:.1f} KB")
            
            # Quick syntax check
            try:
                with open(frontend, 'r') as f:
                    content = f.read()
                compile(content, frontend, 'exec')
                print("   ✅ Syntax valid")
            except Exception as e:
                print(f"   ❌ Syntax error: {e}")
        else:
            print(f"❌ Missing: {frontend}")
    
    print(f"\n📋 Found {len(working_frontends)} potential frontends")
    
    # Check the stable_real_demo.py which was mentioned as working
    stable_demo = "demetify_final_enhanced/stable_real_demo.py"
    if Path(stable_demo).exists():
        print(f"\n🔍 Analyzing {stable_demo} (mentioned as working)...")
        
        with open(stable_demo, 'r') as f:
            content = f.read()
            
        # Check for key components
        has_nilearn = 'nilearn' in content
        has_mri_loading = 'load' in content.lower() and 'mri' in content.lower()
        has_heatmap = 'heatmap' in content.lower()
        has_streamlit = 'streamlit' in content
        
        print(f"   📊 Has nilearn: {'✅' if has_nilearn else '❌'}")
        print(f"   📊 Has MRI loading: {'✅' if has_mri_loading else '❌'}")
        print(f"   📊 Has heatmap: {'✅' if has_heatmap else '❌'}")
        print(f"   📊 Has streamlit: {'✅' if has_streamlit else '❌'}")
        
        if has_nilearn and has_mri_loading and has_heatmap and has_streamlit:
            print("   🎉 This looks like a complete working frontend!")
    
    print("\n🎯 RECOMMENDATIONS:")
    print("1. Check diagnostic images to verify MRI data quality")
    print("2. Test stable_real_demo.py if it exists")
    print("3. Compare with working versions from demetify_final_enhanced/")
    print("4. Ensure proper nilearn visualization setup")

if __name__ == "__main__":
    diagnose_mri_loading()
