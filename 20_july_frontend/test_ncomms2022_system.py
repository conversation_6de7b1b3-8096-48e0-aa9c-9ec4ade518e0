"""
Comprehensive Test Script for NCOMMS2022 System
Tests all three components: Preprocessing, Classification, and SHAP Interpretability
"""

import numpy as np
import os
import sys
import logging
from pathlib import Path
import matplotlib.pyplot as plt
import time

# Add current directory to path
sys.path.append('.')

# Import our components
from ncomms2022_preprocessing_fsl import NCOMMSFSLPreprocessor
from ncomms2022_model_enhanced import NCOMMSClassifier
from ncomms2022_shap import NCOMMSSHAPExplainer

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_preprocessing():
    """Test the preprocessing component"""
    logger.info("=" * 60)
    logger.info("TESTING PREPROCESSING COMPONENT")
    logger.info("=" * 60)
    
    try:
        # Initialize preprocessor
        preprocessor = NCOMMSFSLPreprocessor()
        logger.info(f"✓ Preprocessor initialized successfully")
        logger.info(f"  - FSL available: {preprocessor.use_fsl}")
        logger.info(f"  - FSL directory: {preprocessor.fsl_dir}")
        
        # Test with demo data (already preprocessed .npy files)
        demo_files = [
            "ncomms2022_original/demo/mri/demo1.npy",
            "ncomms2022_original/demo/mri/demo2.npy", 
            "ncomms2022_original/demo/mri/demo3.npy"
        ]
        
        for demo_file in demo_files:
            if os.path.exists(demo_file):
                logger.info(f"✓ Found demo file: {demo_file}")
                data = np.load(demo_file)
                logger.info(f"  - Shape: {data.shape}")
                logger.info(f"  - Data range: [{data.min():.3f}, {data.max():.3f}]")
                logger.info(f"  - Mean: {data.mean():.3f}")
                break
        else:
            logger.warning("No demo files found for preprocessing test")
            return False
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Preprocessing test failed: {e}")
        return False

def test_classification():
    """Test the classification component"""
    logger.info("=" * 60)
    logger.info("TESTING CLASSIFICATION COMPONENT")
    logger.info("=" * 60)
    
    try:
        # Initialize classifier
        logger.info("Initializing classifier...")
        classifier = NCOMMSClassifier()
        logger.info(f"✓ Classifier initialized successfully")
        logger.info(f"  - Device: {classifier.device}")
        logger.info(f"  - ADD model loaded: {classifier.add_model is not None}")
        logger.info(f"  - COG model loaded: {classifier.cog_model is not None}")
        
        # Test with demo data
        demo_file = "ncomms2022_original/demo/mri/demo1.npy"
        if os.path.exists(demo_file):
            logger.info(f"Testing with demo file: {demo_file}")
            mri_data = np.load(demo_file)
            
            # Test ADD prediction
            logger.info("Testing ADD prediction...")
            cn_prob, ad_prob = classifier.predict_add_probability(mri_data)
            logger.info(f"  - CN probability: {cn_prob:.3f}")
            logger.info(f"  - AD probability: {ad_prob:.3f}")
            
            # Test COG prediction
            logger.info("Testing COG prediction...")
            cog_score = classifier.predict_cog_score(mri_data)
            logger.info(f"  - COG score: {cog_score:.3f}")
            
            # Test full prediction
            logger.info("Testing full prediction...")
            results = classifier.predict_full(mri_data)
            logger.info(f"  - Predicted class: {results['predicted_class']}")
            logger.info(f"  - Confidence: {results['confidence']:.3f}")
            logger.info(f"  - Risk level: {results['risk_level']}")
            
            logger.info("✓ Classification test completed successfully")
            return True, classifier
        else:
            logger.error(f"Demo file not found: {demo_file}")
            return False, None
            
    except Exception as e:
        logger.error(f"✗ Classification test failed: {e}")
        return False, None

def test_shap_interpretability(classifier):
    """Test the SHAP interpretability component"""
    logger.info("=" * 60)
    logger.info("TESTING SHAP INTERPRETABILITY COMPONENT")
    logger.info("=" * 60)
    
    try:
        # Check if SHAP is available
        try:
            import shap
            logger.info("✓ SHAP library available")
        except ImportError:
            logger.error("✗ SHAP library not available. Install with: pip install shap")
            return False
        
        # Initialize SHAP explainer
        logger.info("Initializing SHAP explainer...")
        explainer = NCOMMSSHAPExplainer(classifier)
        logger.info(f"✓ SHAP explainer initialized successfully")
        logger.info(f"  - ADD explainer: {explainer.add_explainer is not None}")
        logger.info(f"  - COG explainer: {explainer.cog_explainer is not None}")
        logger.info(f"  - Background samples shape: {explainer.background_samples.shape}")
        
        # Test with demo data
        demo_file = "ncomms2022_original/demo/mri/demo1.npy"
        if os.path.exists(demo_file):
            logger.info(f"Testing SHAP explanation with: {demo_file}")
            mri_data = np.load(demo_file)
            
            # Test ADD SHAP explanation
            logger.info("Generating ADD SHAP explanation...")
            add_shap = explainer.explain_add_prediction(mri_data)
            logger.info(f"  - ADD SHAP shape: {add_shap.shape}")
            logger.info(f"  - ADD SHAP range: [{add_shap.min():.6f}, {add_shap.max():.6f}]")
            
            # Test visualization generation
            logger.info("Generating SHAP visualizations...")
            visualizations = explainer.generate_slice_visualizations(mri_data, add_shap)
            logger.info(f"  - Generated visualizations for: {list(visualizations.keys())}")
            
            # Test full SHAP explanation
            logger.info("Testing full SHAP explanation...")
            shap_results = explainer.explain_full(mri_data)
            logger.info(f"  - SHAP results keys: {list(shap_results.keys())}")
            
            logger.info("✓ SHAP interpretability test completed successfully")
            return True
        else:
            logger.error(f"Demo file not found: {demo_file}")
            return False
            
    except Exception as e:
        logger.error(f"✗ SHAP interpretability test failed: {e}")
        return False

def test_with_radiologist_cohort():
    """Test with real radiologist test cohort data"""
    logger.info("=" * 60)
    logger.info("TESTING WITH RADIOLOGIST TEST COHORT")
    logger.info("=" * 60)
    
    try:
        # Initialize components
        classifier = NCOMMSClassifier()
        explainer = NCOMMSSHAPExplainer(classifier)
        
        # Test files from radiologist cohort
        test_files = [
            "/mnt/z/radiologist_test_cohort_25/T1_ALZHEIMERS_demo_case1.npy",
            "/mnt/z/radiologist_test_cohort_25/T1_NORMAL_demo_case3.npy",
            "/mnt/z/radiologist_test_cohort_25/CN_NACC_S_120572_20190305.npy_age58_mmse30_q0.089.npy",
            "/mnt/z/radiologist_test_cohort_25/AD_NACC_S_063067_20210205.npy_age80_mmse17_q0.100.npy"
        ]
        
        results_summary = []
        
        for test_file in test_files:
            if os.path.exists(test_file):
                logger.info(f"\nTesting with: {os.path.basename(test_file)}")
                
                # Load data
                mri_data = np.load(test_file)
                logger.info(f"  - Data shape: {mri_data.shape}")
                
                # Classification
                results = classifier.predict_full(mri_data)
                logger.info(f"  - Prediction: {results['predicted_class']} (confidence: {results['confidence']:.3f})")
                logger.info(f"  - Risk level: {results['risk_level']}")
                logger.info(f"  - COG score: {results['cog_score']:.2f}")
                
                # Store results
                results_summary.append({
                    'file': os.path.basename(test_file),
                    'prediction': results['predicted_class'],
                    'confidence': results['confidence'],
                    'risk_level': results['risk_level'],
                    'cog_score': results['cog_score'],
                    'cn_prob': results['cn_probability'],
                    'ad_prob': results['ad_probability']
                })
                
                # Test SHAP (only for first file to save time)
                if test_file == test_files[0]:
                    logger.info("  - Generating SHAP explanation...")
                    shap_results = explainer.explain_full(mri_data)
                    logger.info("  - SHAP explanation completed")
            else:
                logger.warning(f"File not found: {test_file}")
        
        # Summary
        logger.info("\n" + "=" * 60)
        logger.info("RADIOLOGIST COHORT TEST SUMMARY")
        logger.info("=" * 60)
        
        for result in results_summary:
            logger.info(f"{result['file'][:30]:<30} | {result['prediction']:<2} | {result['confidence']:.3f} | {result['risk_level']:<8} | COG: {result['cog_score']:.2f}")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Radiologist cohort test failed: {e}")
        return False

def main():
    """Run all tests"""
    logger.info("🧠 NCOMMS2022 SYSTEM COMPREHENSIVE TEST")
    logger.info("=" * 80)
    
    start_time = time.time()
    
    # Test 1: Preprocessing
    preprocessing_success = test_preprocessing()
    
    # Test 2: Classification
    classification_success, classifier = test_classification()
    
    # Test 3: SHAP Interpretability
    if classification_success and classifier is not None:
        shap_success = test_shap_interpretability(classifier)
    else:
        shap_success = False
        logger.warning("Skipping SHAP test due to classification failure")
    
    # Test 4: Real data test
    real_data_success = test_with_radiologist_cohort()
    
    # Final summary
    end_time = time.time()
    duration = end_time - start_time
    
    logger.info("\n" + "=" * 80)
    logger.info("FINAL TEST SUMMARY")
    logger.info("=" * 80)
    logger.info(f"Preprocessing:        {'✓ PASS' if preprocessing_success else '✗ FAIL'}")
    logger.info(f"Classification:       {'✓ PASS' if classification_success else '✗ FAIL'}")
    logger.info(f"SHAP Interpretability: {'✓ PASS' if shap_success else '✗ FAIL'}")
    logger.info(f"Real Data Test:       {'✓ PASS' if real_data_success else '✗ FAIL'}")
    logger.info(f"Total Duration:       {duration:.2f} seconds")
    
    all_tests_passed = all([preprocessing_success, classification_success, shap_success, real_data_success])
    
    if all_tests_passed:
        logger.info("\n🎉 ALL TESTS PASSED! System is ready for deployment.")
    else:
        logger.info("\n⚠️  Some tests failed. Please check the logs above.")
    
    return all_tests_passed

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
