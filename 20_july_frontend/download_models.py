"""
Model Download Utility for NCOMMs2022 Frontend
This script helps download and organize pretrained model weights.
"""

import os
import sys
import requests
import zipfile
from pathlib import Path
import argparse
from tqdm import tqdm

def download_file(url, destination, chunk_size=8192):
    """
    Download a file with progress bar.
    
    Args:
        url: URL to download from
        destination: Local file path to save to
        chunk_size: Download chunk size in bytes
    """
    try:
        response = requests.get(url, stream=True)
        response.raise_for_status()
        
        total_size = int(response.headers.get('content-length', 0))
        
        with open(destination, 'wb') as file, tqdm(
            desc=destination.name,
            total=total_size,
            unit='B',
            unit_scale=True,
            unit_divisor=1024,
        ) as pbar:
            for chunk in response.iter_content(chunk_size=chunk_size):
                if chunk:
                    file.write(chunk)
                    pbar.update(len(chunk))
        
        print(f"✅ Downloaded: {destination}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to download {url}: {e}")
        return False

def extract_zip(zip_path, extract_to):
    """
    Extract a zip file.
    
    Args:
        zip_path: Path to zip file
        extract_to: Directory to extract to
    """
    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_ref:
            zip_ref.extractall(extract_to)
        print(f"✅ Extracted: {zip_path}")
        return True
    except Exception as e:
        print(f"❌ Failed to extract {zip_path}: {e}")
        return False

def check_model_availability():
    """
    Check which models are already available locally.
    """
    checkpoint_dir = Path("ncomms2022/checkpoint_dir")
    
    if not checkpoint_dir.exists():
        print("❌ Checkpoint directory not found")
        return []
    
    available_models = []
    required_files = ["backbone_58.pth", "ADD_58.pth", "COG_58.pth"]
    
    for model_dir in checkpoint_dir.iterdir():
        if model_dir.is_dir():
            if all((model_dir / f).exists() for f in required_files):
                available_models.append(model_dir.name)
    
    return available_models

def setup_demo_data():
    """
    Set up demo data for testing the frontend.
    """
    print("🔧 Setting up demo data...")
    
    demo_dir = Path("demo_data")
    demo_dir.mkdir(exist_ok=True)
    
    # Create a sample CSV file for demo
    demo_csv_content = """filename,path,ADD,COG
demo_sample.npy,./demo_data/,1,2.1
"""
    
    with open(demo_dir / "demo.csv", "w") as f:
        f.write(demo_csv_content)
    
    # Create a dummy MRI file for demo (if it doesn't exist)
    demo_mri_path = demo_dir / "demo_sample.npy"
    if not demo_mri_path.exists():
        # Create dummy MRI data with correct shape
        import numpy as np
        dummy_mri = np.random.rand(121, 145, 121).astype(np.float32)
        np.save(demo_mri_path, dummy_mri)
        print(f"✅ Created demo MRI file: {demo_mri_path}")
    
    print("✅ Demo data setup complete")

def verify_ncomms2022_repo():
    """
    Verify that the ncomms2022 repository is properly set up.
    """
    print("🔍 Verifying ncomms2022 repository setup...")
    
    ncomms_dir = Path("ncomms2022")
    if not ncomms_dir.exists():
        print("❌ ncomms2022 directory not found")
        print("💡 Please clone the repository first:")
        print("   git clone https://github.com/vkola-lab/ncomms2022.git")
        return False
    
    # Check for essential files
    essential_files = [
        "ncomms2022/models.py",
        "ncomms2022/utils.py",
        "ncomms2022/task_config.json",
        "ncomms2022/backends"
    ]
    
    missing_files = []
    for file_path in essential_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print("❌ Missing essential files:")
        for file_path in missing_files:
            print(f"   - {file_path}")
        return False
    
    print("✅ ncomms2022 repository structure verified")
    return True

def copy_essential_files():
    """
    Copy essential files from ncomms2022 to the current directory.
    """
    print("📁 Copying essential files...")
    
    import shutil
    
    files_to_copy = [
        ("ncomms2022/models.py", "models.py"),
        ("ncomms2022/utils.py", "utils.py"),
        ("ncomms2022/task_config.json", "task_config.json")
    ]
    
    dirs_to_copy = [
        ("ncomms2022/backends", "backends")
    ]
    
    # Copy files
    for src, dst in files_to_copy:
        if Path(src).exists():
            shutil.copy2(src, dst)
            print(f"✅ Copied: {src} -> {dst}")
        else:
            print(f"❌ Source file not found: {src}")
    
    # Copy directories
    for src, dst in dirs_to_copy:
        if Path(src).exists():
            if Path(dst).exists():
                shutil.rmtree(dst)
            shutil.copytree(src, dst)
            print(f"✅ Copied directory: {src} -> {dst}")
        else:
            print(f"❌ Source directory not found: {src}")

def main():
    """Main function to handle model download and setup."""
    parser = argparse.ArgumentParser(description="NCOMMs2022 Model Download Utility")
    parser.add_argument("--setup-demo", action="store_true", help="Set up demo data")
    parser.add_argument("--copy-files", action="store_true", help="Copy essential files from ncomms2022")
    parser.add_argument("--check", action="store_true", help="Check current setup status")
    
    args = parser.parse_args()
    
    print("🧠 NCOMMs2022 Model Download Utility")
    print("=" * 50)
    
    # Verify repository setup
    if not verify_ncomms2022_repo():
        print("\n❌ Repository setup incomplete. Please clone ncomms2022 first.")
        return
    
    if args.copy_files:
        copy_essential_files()
        print()
    
    if args.setup_demo:
        setup_demo_data()
        print()
    
    if args.check:
        print("📊 Current Setup Status:")
        print("-" * 30)
        
        # Check available models
        available_models = check_model_availability()
        print(f"Available models: {len(available_models)}")
        for model in available_models:
            print(f"  ✅ {model}")
        
        # Check essential files
        essential_files = ["models.py", "utils.py", "task_config.json", "backends"]
        print(f"\nEssential files:")
        for file_name in essential_files:
            if Path(file_name).exists():
                print(f"  ✅ {file_name}")
            else:
                print(f"  ❌ {file_name}")
        
        # Check demo data
        if Path("demo_data").exists():
            print(f"\n✅ Demo data directory exists")
        else:
            print(f"\n❌ Demo data directory not found")
        
        return
    
    # Default behavior: provide setup instructions
    if not any([args.setup_demo, args.copy_files, args.check]):
        print("🚀 Setup Instructions:")
        print("-" * 30)
        print("1. Clone the ncomms2022 repository (already done)")
        print("2. Copy essential files:")
        print("   python download_models.py --copy-files")
        print("3. Set up demo data:")
        print("   python download_models.py --setup-demo")
        print("4. Check setup status:")
        print("   python download_models.py --check")
        print("5. Test the setup:")
        print("   python test_ncomms2022_setup.py")
        print("6. Run the frontend:")
        print("   streamlit run ncomms2022_frontend.py")
        
        print("\n💡 Note: The pretrained model weights are already included")
        print("   in the cloned ncomms2022 repository.")
        
        # Check current status
        available_models = check_model_availability()
        if available_models:
            print(f"\n✅ Found {len(available_models)} pretrained models ready to use!")
        else:
            print(f"\n⚠️ No complete pretrained models found.")
            print("   Please ensure the ncomms2022/checkpoint_dir/ contains model weights.")

if __name__ == "__main__":
    main()
