#!/usr/bin/env python3
"""
Quick Gradient Fix - Train a simple model locally with proper gradients
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import json
from pathlib import Path

class SimpleGradientModel(nn.Module):
    """Simple model designed to produce meaningful gradients"""
    
    def __init__(self):
        super(SimpleGradientModel, self).__init__()
        
        self.features = nn.Sequential(
            nn.Conv3d(1, 8, kernel_size=7, padding=3),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            
            nn.Conv3d(8, 16, kernel_size=5, padding=2),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            
            nn.Conv3d(16, 32, kernel_size=3, padding=1),
            nn.ReLU(inplace=True),
            nn.AdaptiveAvgPool3d((4, 4, 4))
        )
        
        self.classifier = nn.Sequential(
            nn.Linear(32 * 4 * 4 * 4, 128),
            nn.<PERSON><PERSON>(),
            nn.Dropout(0.3),
            nn.Linear(128, 64),
            nn.<PERSON><PERSON>(),
            nn.Dropout(0.3),
            nn.Linear(64, 1)  # Cognitive score output
        )
    
    def forward(self, x):
        features = self.features(x)
        features = features.view(features.size(0), -1)
        cognitive_score = self.classifier(features)
        
        # Scale to 0-30 range
        cognitive_score = torch.sigmoid(cognitive_score) * 30.0
        
        return {
            'cognitive_score': cognitive_score,
            'features': features
        }

def create_training_data(num_samples=200):
    """Create simple training data with clear patterns"""
    
    data = []
    scores = []
    
    for i in range(num_samples):
        # Create MRI with varying intensity patterns
        base_intensity = np.random.uniform(0.2, 0.8)
        mri = np.random.normal(base_intensity, 0.1, (91, 109, 91))
        
        # Add brain-like structure
        center = (45, 54, 45)
        for x in range(center[0]-20, center[0]+20):
            for y in range(center[1]-25, center[1]+25):
                for z in range(center[2]-20, center[2]+20):
                    if 0 <= x < 91 and 0 <= y < 109 and 0 <= z < 91:
                        dist = np.sqrt((x-center[0])**2 + (y-center[1])**2 + (z-center[2])**2)
                        if dist < 20:
                            mri[x, y, z] += np.random.normal(0.1, 0.05)
        
        # Create cognitive score based on intensity (clear relationship)
        avg_intensity = np.mean(mri[30:60, 40:70, 30:60])  # Central region
        
        # Higher intensity = higher cognitive score
        if avg_intensity > 0.6:
            score = np.random.normal(27, 2)  # CN
        elif avg_intensity > 0.4:
            score = np.random.normal(22, 2)  # MCI
        else:
            score = np.random.normal(16, 3)  # AD
        
        score = np.clip(score, 8, 30)
        
        data.append(mri)
        scores.append(score)
    
    return np.array(data), np.array(scores)

def train_gradient_model():
    """Train a simple model with proper gradients"""
    
    print("🔧 Training simple gradient model...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Create training data
    train_data, train_scores = create_training_data(150)
    val_data, val_scores = create_training_data(50)
    
    # Convert to tensors
    train_tensor = torch.FloatTensor(train_data).unsqueeze(1)
    train_scores_tensor = torch.FloatTensor(train_scores)
    val_tensor = torch.FloatTensor(val_data).unsqueeze(1)
    val_scores_tensor = torch.FloatTensor(val_scores)
    
    # Model
    model = SimpleGradientModel().to(device)
    optimizer = optim.Adam(model.parameters(), lr=0.001)
    criterion = nn.MSELoss()
    
    # Training
    epochs = 20
    batch_size = 4
    
    for epoch in range(epochs):
        model.train()
        total_loss = 0
        
        for i in range(0, len(train_tensor), batch_size):
            batch_data = train_tensor[i:i+batch_size].to(device)
            batch_scores = train_scores_tensor[i:i+batch_size].to(device)
            
            optimizer.zero_grad()
            outputs = model(batch_data)
            loss = criterion(outputs['cognitive_score'].squeeze(), batch_scores)
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
        
        # Validation
        model.eval()
        val_loss = 0
        with torch.no_grad():
            for i in range(0, len(val_tensor), batch_size):
                batch_data = val_tensor[i:i+batch_size].to(device)
                batch_scores = val_scores_tensor[i:i+batch_scores].to(device)
                
                outputs = model(batch_data)
                loss = criterion(outputs['cognitive_score'].squeeze(), batch_scores)
                val_loss += loss.item()
        
        print(f'Epoch {epoch+1}: Train Loss: {total_loss/len(train_tensor)*batch_size:.4f}, Val Loss: {val_loss/len(val_tensor)*batch_size:.4f}')
    
    # Test gradient computation
    print("\n🧪 Testing gradient computation...")
    model.eval()
    test_input = train_tensor[0:1].to(device)
    test_input.requires_grad_(True)
    
    outputs = model(test_input)
    loss = outputs['cognitive_score'].mean()
    loss.backward()
    
    gradients = test_input.grad
    if gradients is not None:
        grad_magnitude = torch.abs(gradients).cpu().numpy()
        print(f"✅ Gradients computed successfully!")
        print(f"   Gradient range: {grad_magnitude.min():.6f} - {grad_magnitude.max():.6f}")
        print(f"   Non-zero gradients: {np.count_nonzero(grad_magnitude)}")
    else:
        print("❌ No gradients computed")
        return False
    
    # Save model
    torch.save({
        'model_state_dict': model.state_dict(),
        'gradient_test_passed': True
    }, 'gradient_fixed_model.pth')
    
    print("✅ Gradient-enabled model saved as 'gradient_fixed_model.pth'")
    return True

if __name__ == "__main__":
    success = train_gradient_model()
    if success:
        print("\n🎉 GRADIENT FIX COMPLETE!")
        print("Model with proper gradients is ready for heatmap generation.")
    else:
        print("\n❌ GRADIENT FIX FAILED!")
