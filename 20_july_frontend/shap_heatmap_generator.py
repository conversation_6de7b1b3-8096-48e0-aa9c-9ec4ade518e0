#!/usr/bin/env python3
"""
REAL SHAP-based heatmap generator for MRI classification models
"""

import numpy as np
import torch
import torch.nn.functional as F
import logging
from pathlib import Path

logger = logging.getLogger(__name__)

class SHAPHeatmapGenerator:
    """Generate REAL SHAP interpretability heatmaps from classification models"""
    
    def __init__(self, device='cuda' if torch.cuda.is_available() else 'cpu'):
        self.device = device
        logger.info(f"🔥 SHAP Heatmap Generator initialized on {device}")
    
    def generate_shap_heatmap(self, model, mri_tensor, target_class=None):
        """Generate REAL SHAP heatmap using gradient-based attribution"""
        
        logger.info("🧠 Generating REAL SHAP heatmap from classification model")
        
        try:
            # Ensure model is in eval mode
            model.eval()
            
            # Ensure tensor requires gradients
            mri_tensor = mri_tensor.clone().detach().requires_grad_(True)
            
            # Forward pass
            with torch.enable_grad():
                outputs = model(mri_tensor)
                
                # Handle different model output formats
                if isinstance(outputs, dict):
                    # Multi-head model (like your models)
                    if 'clinical_head' in outputs:
                        logits = outputs['clinical_head']
                    elif 'ADD' in outputs:
                        logits = outputs['ADD']
                    elif 'mmse_head' in outputs:
                        # For regression models, use MMSE prediction
                        logits = outputs['mmse_head']
                    else:
                        # Use first available output
                        logits = list(outputs.values())[0]
                else:
                    logits = outputs
                
                # Determine target for gradient computation
                if target_class is None:
                    if logits.dim() > 1 and logits.size(1) > 1:
                        # Classification: use predicted class
                        target_class = torch.argmax(logits, dim=1)
                    else:
                        # Regression: use the prediction itself
                        target_class = 0
                
                # Compute target score
                if logits.dim() > 1 and logits.size(1) > 1:
                    # Classification model
                    if isinstance(target_class, int):
                        target_score = logits[0, target_class]
                    else:
                        target_score = logits.gather(1, target_class.unsqueeze(1)).squeeze()
                else:
                    # Regression model
                    target_score = logits.squeeze()
                
                logger.info(f"Target score: {target_score.item():.4f}, Logits shape: {logits.shape}")
                
                # Compute gradients (SHAP-like attribution)
                target_score.backward()
                
                if mri_tensor.grad is not None:
                    # Get gradients
                    gradients = mri_tensor.grad[0, 0].cpu().numpy()  # Remove batch and channel dims
                    
                    # Apply input * gradient (Integrated Gradients approximation)
                    input_data = mri_tensor[0, 0].detach().cpu().numpy()
                    attribution = gradients * input_data
                    
                    # Take absolute value for heatmap
                    heatmap = np.abs(attribution)
                    
                    # Smooth the heatmap
                    heatmap = self._smooth_heatmap(heatmap)
                    
                    # Normalize
                    if heatmap.max() > heatmap.min():
                        heatmap = (heatmap - heatmap.min()) / (heatmap.max() - heatmap.min())
                    
                    logger.info(f"✅ SHAP heatmap generated: range {heatmap.min():.6f}-{heatmap.max():.6f}")
                    
                    return heatmap
                else:
                    logger.warning("No gradients computed - model may not support gradients")
                    return None
                    
        except Exception as e:
            logger.error(f"SHAP heatmap generation failed: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def generate_integrated_gradients_heatmap(self, model, mri_tensor, target_class=None, steps=50):
        """Generate more accurate SHAP heatmap using Integrated Gradients"""
        
        logger.info(f"🔥 Generating Integrated Gradients heatmap with {steps} steps")
        
        try:
            model.eval()
            
            # Create baseline (zeros)
            baseline = torch.zeros_like(mri_tensor)
            
            # Generate path from baseline to input
            alphas = torch.linspace(0, 1, steps).to(self.device)
            
            gradients_list = []
            
            for alpha in alphas:
                # Interpolate between baseline and input
                interpolated = baseline + alpha * (mri_tensor - baseline)
                interpolated.requires_grad_(True)
                
                # Forward pass
                outputs = model(interpolated)
                
                # Handle different output formats
                if isinstance(outputs, dict):
                    if 'clinical_head' in outputs:
                        logits = outputs['clinical_head']
                    elif 'ADD' in outputs:
                        logits = outputs['ADD']
                    elif 'mmse_head' in outputs:
                        logits = outputs['mmse_head']
                    else:
                        logits = list(outputs.values())[0]
                else:
                    logits = outputs
                
                # Compute target score
                if target_class is None:
                    if logits.dim() > 1 and logits.size(1) > 1:
                        target_class = torch.argmax(logits, dim=1)
                    else:
                        target_class = 0
                
                if logits.dim() > 1 and logits.size(1) > 1:
                    if isinstance(target_class, int):
                        target_score = logits[0, target_class]
                    else:
                        target_score = logits.gather(1, target_class.unsqueeze(1)).squeeze()
                else:
                    target_score = logits.squeeze()
                
                # Compute gradients
                target_score.backward()
                
                if interpolated.grad is not None:
                    gradients_list.append(interpolated.grad[0, 0].cpu().numpy())
                
                # Clear gradients
                model.zero_grad()
            
            if gradients_list:
                # Average gradients
                avg_gradients = np.mean(gradients_list, axis=0)
                
                # Multiply by (input - baseline)
                input_diff = (mri_tensor - baseline)[0, 0].cpu().numpy()
                attribution = avg_gradients * input_diff
                
                # Create heatmap
                heatmap = np.abs(attribution)
                
                # Smooth and normalize
                heatmap = self._smooth_heatmap(heatmap)
                
                if heatmap.max() > heatmap.min():
                    heatmap = (heatmap - heatmap.min()) / (heatmap.max() - heatmap.min())
                
                logger.info(f"✅ Integrated Gradients heatmap generated")
                
                return heatmap
            else:
                logger.warning("No gradients computed in Integrated Gradients")
                return None
                
        except Exception as e:
            logger.error(f"Integrated Gradients failed: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def _smooth_heatmap(self, heatmap, sigma=1.5):
        """Apply Gaussian smoothing to heatmap"""
        try:
            from scipy.ndimage import gaussian_filter
            return gaussian_filter(heatmap, sigma=sigma)
        except ImportError:
            logger.warning("scipy not available, skipping smoothing")
            return heatmap
    
    def generate_occlusion_heatmap(self, model, mri_tensor, patch_size=8, stride=4):
        """Generate heatmap using occlusion analysis"""
        
        logger.info(f"🔍 Generating occlusion heatmap with patch size {patch_size}")
        
        try:
            model.eval()
            
            # Get original prediction
            with torch.no_grad():
                original_output = model(mri_tensor)
                
                if isinstance(original_output, dict):
                    if 'clinical_head' in original_output:
                        original_logits = original_output['clinical_head']
                    elif 'ADD' in original_output:
                        original_logits = original_output['ADD']
                    else:
                        original_logits = list(original_output.values())[0]
                else:
                    original_logits = original_output
                
                if original_logits.dim() > 1 and original_logits.size(1) > 1:
                    original_score = torch.max(original_logits, dim=1)[0].item()
                else:
                    original_score = original_logits.item()
            
            # Create heatmap
            input_shape = mri_tensor.shape[2:]  # Remove batch and channel dims
            heatmap = np.zeros(input_shape)
            
            # Occlude patches and measure impact
            for x in range(0, input_shape[0] - patch_size + 1, stride):
                for y in range(0, input_shape[1] - patch_size + 1, stride):
                    for z in range(0, input_shape[2] - patch_size + 1, stride):
                        # Create occluded input
                        occluded_input = mri_tensor.clone()
                        occluded_input[0, 0, x:x+patch_size, y:y+patch_size, z:z+patch_size] = 0
                        
                        # Get prediction
                        with torch.no_grad():
                            occluded_output = model(occluded_input)
                            
                            if isinstance(occluded_output, dict):
                                if 'clinical_head' in occluded_output:
                                    occluded_logits = occluded_output['clinical_head']
                                elif 'ADD' in occluded_output:
                                    occluded_logits = occluded_output['ADD']
                                else:
                                    occluded_logits = list(occluded_output.values())[0]
                            else:
                                occluded_logits = occluded_output
                            
                            if occluded_logits.dim() > 1 and occluded_logits.size(1) > 1:
                                occluded_score = torch.max(occluded_logits, dim=1)[0].item()
                            else:
                                occluded_score = occluded_logits.item()
                        
                        # Calculate importance (drop in prediction)
                        importance = original_score - occluded_score
                        
                        # Assign to heatmap
                        heatmap[x:x+patch_size, y:y+patch_size, z:z+patch_size] = max(
                            heatmap[x:x+patch_size, y:y+patch_size, z:z+patch_size].max(),
                            importance
                        )
            
            # Normalize
            if heatmap.max() > heatmap.min():
                heatmap = (heatmap - heatmap.min()) / (heatmap.max() - heatmap.min())
            
            logger.info(f"✅ Occlusion heatmap generated")
            
            return heatmap
            
        except Exception as e:
            logger.error(f"Occlusion heatmap failed: {e}")
            return None
