#!/usr/bin/env python3
"""
Test Final MRI-Based Heatmaps
"""

import numpy as np
import matplotlib.pyplot as plt
from final_mci_streamlit_app import FinalMCIInferenceEngine
import time

def test_mri_based_heatmaps():
    """Test the new MRI-based heatmap generation"""
    
    print("🔥 Testing MRI-Based Heatmap Generation...")
    
    engine = FinalMCIInferenceEngine()
    
    # Create different types of MRI data
    test_cases = [
        ("Healthy Brain", np.random.normal(0.6, 0.1, (91, 109, 91)), 28.0),
        ("MCI Brain", np.random.normal(0.4, 0.15, (91, 109, 91)), 22.0),
        ("AD Brain", np.random.normal(0.25, 0.2, (91, 109, 91)), 16.0)
    ]
    
    results = []
    
    for case_name, mri_data, expected_score in test_cases:
        print(f"\n📊 Testing {case_name} (Expected Score: {expected_score})...")
        
        start_time = time.time()
        result = engine.comprehensive_predict(mri_data)
        processing_time = time.time() - start_time
        
        print(f"   Processing time: {processing_time:.2f}s")
        
        for model_name, model_results in result['models'].items():
            cognitive_score = model_results['cognitive_score']
            predicted_class = ['CN', 'MCI', 'AD'][model_results['predicted_class']]
            
            print(f"   {model_name}:")
            print(f"     Cognitive Score: {cognitive_score:.1f}")
            print(f"     Predicted Class: {predicted_class}")
            
            if 'heatmap' in model_results:
                heatmap = model_results['heatmap']
                print(f"     Heatmap range: {heatmap.min():.4f} - {heatmap.max():.4f}")
                print(f"     Non-zero values: {np.count_nonzero(heatmap)}")
                
                # Save heatmap visualization
                mid_slice = heatmap[:, :, 45]
                plt.figure(figsize=(10, 8))
                
                # Create subplot with MRI and heatmap
                plt.subplot(1, 2, 1)
                plt.imshow(mri_data[:, :, 45], cmap='gray')
                plt.title(f'{case_name} - Original MRI')
                plt.axis('off')
                
                plt.subplot(1, 2, 2)
                plt.imshow(mid_slice, cmap='hot')
                plt.colorbar(label='Attention Intensity')
                plt.title(f'{model_name} - Heatmap\nScore: {cognitive_score:.1f}')
                plt.axis('off')
                
                plt.tight_layout()
                filename = f'{case_name.lower().replace(" ", "_")}_{model_name.lower().replace(" ", "_")}_heatmap.png'
                plt.savefig(filename, dpi=150, bbox_inches='tight')
                plt.close()
                
                print(f"     ✅ Heatmap saved as {filename}")
                
                # Check brain region analysis
                if 'brain_regions' in model_results:
                    brain_analysis = model_results['brain_regions']
                    affected_regions = brain_analysis['affected_regions']
                    print(f"     Affected regions: {len(affected_regions)}")
                    if affected_regions:
                        print(f"       - {', '.join(affected_regions[:3])}{'...' if len(affected_regions) > 3 else ''}")
                
                results.append({
                    'case': case_name,
                    'model': model_name,
                    'score': cognitive_score,
                    'heatmap_quality': heatmap.max(),
                    'affected_regions': len(affected_regions) if 'brain_regions' in model_results else 0
                })
    
    return results

def test_heatmap_variation():
    """Test that heatmaps vary with different inputs"""
    
    print("\n🎯 Testing Heatmap Variation...")
    
    engine = FinalMCIInferenceEngine()
    
    # Create 5 different MRI scans
    heatmap_stats = []
    
    for i in range(5):
        # Create varied MRI data
        intensity = 0.3 + (i * 0.1)  # 0.3 to 0.7
        mri_data = np.random.normal(intensity, 0.15, (91, 109, 91))
        
        result = engine.comprehensive_predict(mri_data)
        
        for model_name, model_results in result['models'].items():
            if 'heatmap' in model_results:
                heatmap = model_results['heatmap']
                stats = {
                    'scan': i+1,
                    'intensity': intensity,
                    'cognitive_score': model_results['cognitive_score'],
                    'heatmap_max': heatmap.max(),
                    'heatmap_mean': heatmap.mean(),
                    'non_zero': np.count_nonzero(heatmap)
                }
                heatmap_stats.append(stats)
                
                print(f"   Scan {i+1}: Score={stats['cognitive_score']:.1f}, "
                      f"Heatmap max={stats['heatmap_max']:.3f}, "
                      f"Non-zero={stats['non_zero']}")
    
    # Check variation
    scores = [s['cognitive_score'] for s in heatmap_stats]
    heatmap_maxes = [s['heatmap_max'] for s in heatmap_stats]
    
    score_variation = max(scores) - min(scores)
    heatmap_variation = max(heatmap_maxes) - min(heatmap_maxes)
    
    print(f"\n📈 Variation Analysis:")
    print(f"   Cognitive score range: {min(scores):.1f} - {max(scores):.1f} (variation: {score_variation:.1f})")
    print(f"   Heatmap intensity range: {min(heatmap_maxes):.3f} - {max(heatmap_maxes):.3f} (variation: {heatmap_variation:.3f})")
    
    if score_variation > 3.0 and heatmap_variation > 0.1:
        print("✅ Good variation in both scores and heatmaps!")
        return True
    elif score_variation > 2.0:
        print("✅ Reasonable variation in cognitive scores")
        return True
    else:
        print("⚠️ Limited variation - may need improvement")
        return False

def test_brain_region_explanations():
    """Test the brain region explanations"""
    
    print("\n🧠 Testing Brain Region Explanations...")
    
    engine = FinalMCIInferenceEngine()
    
    # Create AD-like MRI (low intensity = atrophy)
    ad_mri = np.random.normal(0.25, 0.2, (91, 109, 91))
    
    result = engine.comprehensive_predict(ad_mri)
    
    for model_name, model_results in result['models'].items():
        print(f"\n🤖 {model_name} Analysis:")
        
        if 'brain_regions' in model_results:
            brain_analysis = model_results['brain_regions']
            
            print(f"   Cognitive Score: {model_results['cognitive_score']:.1f}")
            print(f"   Overall Interpretation: {brain_analysis['interpretation'][:100]}...")
            
            # Show affected regions
            if brain_analysis['affected_regions']:
                print(f"   Affected Regions ({len(brain_analysis['affected_regions'])}):")
                
                for region in brain_analysis['affected_regions'][:3]:  # Show first 3
                    if region in brain_analysis['severity_scores']:
                        region_info = brain_analysis['severity_scores'][region]
                        interpretation = region_info['interpretation']
                        
                        print(f"     🧠 {region}:")
                        print(f"       - Severity: {interpretation['severity_text']}")
                        print(f"       - Function: {interpretation['function'][:50]}...")
                        print(f"       - Clinical Note: {interpretation['clinical_note'][:60]}...")
            
            # Show clinical significance
            if brain_analysis['clinical_significance']:
                print(f"   Clinical Significance:")
                for significance in brain_analysis['clinical_significance'][:2]:
                    print(f"     - {significance}")
            
            return True
        else:
            print("   ❌ No brain region analysis found")
            return False

def main():
    """Test all final heatmap features"""
    
    print("🧪 TESTING FINAL MRI-BASED HEATMAPS")
    print("=" * 60)
    
    # Test 1: MRI-based heatmap generation
    print("Test 1: MRI-Based Heatmap Generation")
    results1 = test_mri_based_heatmaps()
    
    # Test 2: Heatmap variation
    print("\nTest 2: Heatmap Variation")
    success2 = test_heatmap_variation()
    
    # Test 3: Brain region explanations
    print("\nTest 3: Brain Region Explanations")
    success3 = test_brain_region_explanations()
    
    # Summary
    print(f"\n{'='*20} FINAL SUMMARY {'='*20}")
    
    if results1 and success2 and success3:
        print("🎉 ALL HEATMAP FEATURES WORKING!")
        print("✅ MRI-based heatmap generation")
        print("✅ Dynamic variation per case")
        print("✅ Detailed brain region analysis")
        print("✅ Clinical explanations")
        print("\n🚀 READY FOR PRESENTATION!")
        
        print(f"\n📁 Generated {len(results1)} heatmap visualizations")
        print("🔍 Check the saved PNG files for visual verification")
        
    else:
        print("⚠️ Some features need improvement")
        print(f"Heatmap generation: {'✅' if results1 else '❌'}")
        print(f"Variation testing: {'✅' if success2 else '❌'}")
        print(f"Brain explanations: {'✅' if success3 else '❌'}")

if __name__ == "__main__":
    main()
