#!/bin/bash
# Demetify NCOMMS2022 Deployment Script

echo "🧠 Demetify - NCOMMS2022 Frontend Deployment"
echo "============================================="

# Check if conda is available
if ! command -v conda &> /dev/null; then
    echo "❌ Conda not found. Please install Miniconda/Anaconda first."
    exit 1
fi

# Activate environment
echo "📦 Activating conda environment 'abstract'..."
eval "$(conda shell.bash hook)"
conda activate abstract

# Install requirements
echo "📦 Installing Python requirements..."
pip install -r requirements_frontend.txt

# Check if models exist
if [ ! -d "ncomms2022_original/checkpoint_dir" ]; then
    echo "❌ Model checkpoints not found. Please ensure ncomms2022_original is properly set up."
    exit 1
fi

echo "✅ Setup complete!"
echo ""
echo "🚀 Starting Demetify Frontend..."
echo "   Access the application at: http://localhost:8501"
echo "   Press Ctrl+C to stop the server"
echo ""

# Start Streamlit
streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address 0.0.0.0
