"""
Demetify - AI-Powered Radiologist Assistant
Advanced MRI-based dementia assessment tool to accelerate radiological diagnosis.
"""

import streamlit as st
import numpy as np
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import tempfile
import os
from pathlib import Path
import torch
from datetime import datetime
import base64
from io import BytesIO
import matplotlib.pyplot as plt
import matplotlib.patches as patches
import nibabel as nib

# Import our custom modules
from ncomms2022_preprocessing import NCOMMs2022Preprocessor
from ncomms2022_model import NCOMMs2022Model, ModelManager

# Page configuration
st.set_page_config(
    page_title="Demetify - AI Radiologist Assistant",
    page_icon="🧠",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for medical theme
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        color: #2E86AB;
        text-align: center;
        margin-bottom: 2rem;
        font-weight: bold;
    }
    .sub-header {
        font-size: 1.5rem;
        color: #A23B72;
        margin-bottom: 1rem;
    }
    .metric-card {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        border-left: 4px solid #2E86AB;
        margin: 0.5rem 0;
    }
    .prediction-high {
        background-color: #ffe6e6;
        border-left-color: #dc3545;
    }
    .prediction-normal {
        background-color: #e6f7e6;
        border-left-color: #28a745;
    }
    .stAlert > div {
        padding: 1rem;
    }
</style>
""", unsafe_allow_html=True)

def main():
    # Header with branding
    st.markdown("""
    <div style="text-align: center; margin-bottom: 2rem;">
        <h1 style="font-size: 3rem; color: #2E86AB; margin-bottom: 0.5rem; font-weight: bold;">
            🧠 Demetify
        </h1>
        <h2 style="font-size: 1.5rem; color: #A23B72; margin-bottom: 1rem; font-weight: normal;">
            AI-Powered Radiologist Assistant
        </h2>
        <p style="font-size: 1.1rem; color: #666; margin-bottom: 1rem;">
            Accelerating dementia diagnosis through advanced MRI analysis
        </p>
        <div style="display: flex; justify-content: center; align-items: center; gap: 2rem; margin-bottom: 1rem;">
            <div style="text-align: center;">
                <div style="width: 80px; height: 80px; background-color: #E84A27; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 0.5rem;">
                    <span style="color: white; font-weight: bold; font-size: 24px;">UI</span>
                </div>
                <p style="font-size: 0.9rem; color: #888; margin: 0;">University of Illinois<br>Urbana-Champaign</p>
            </div>
        </div>
        <p style="font-size: 0.9rem; color: #888;">
            <strong>Project Lead:</strong> S. Seshadri | <strong>Purpose:</strong> Radiologist Decision Support
        </p>
    </div>
    """, unsafe_allow_html=True)
    
    # Initialize session state
    if 'preprocessor' not in st.session_state:
        st.session_state.preprocessor = NCOMMs2022Preprocessor()
    if 'model_manager' not in st.session_state:
        st.session_state.model_manager = ModelManager()
    if 'current_model' not in st.session_state:
        st.session_state.current_model = None
    if 'preprocessed_data' not in st.session_state:
        st.session_state.preprocessed_data = None
    if 'predictions' not in st.session_state:
        st.session_state.predictions = None
    if 'pdf_status' not in st.session_state:
        st.session_state.pdf_status = 'not_started'  # not_started, generating, ready, error
    if 'pdf_data' not in st.session_state:
        st.session_state.pdf_data = None
    if 'pdf_filename' not in st.session_state:
        st.session_state.pdf_filename = None
    
    # Sidebar for model selection and settings
    with st.sidebar:
        st.markdown('<h2 class="sub-header">⚙️ Model Configuration</h2>', unsafe_allow_html=True)
        
        # Model selection
        available_models = st.session_state.model_manager.get_available_models()
        
        if not available_models:
            st.error("❌ No pretrained models found. Please ensure the ncomms2022 checkpoint directory is available.")
            st.info("💡 Expected location: `ncomms2022/checkpoint_dir/`")
            return
        
        selected_model = st.selectbox(
            "Select Pretrained Model:",
            available_models,
            help="Choose from available cross-validation folds"
        )
        
        # Device selection
        device_options = ['cpu']
        if torch.cuda.is_available():
            device_options.append('cuda')
        
        selected_device = st.selectbox(
            "Compute Device:",
            device_options,
            help="GPU acceleration requires CUDA-compatible hardware"
        )
        
        # Load model button
        if st.button("🔄 Load Model", type="primary"):
            with st.spinner("Loading model..."):
                st.session_state.current_model = st.session_state.model_manager.load_model(
                    selected_model, 
                    device=selected_device
                )
        
        # Model status
        if st.session_state.current_model:
            st.success("✅ Model loaded successfully!")
            
            # Model info
            with st.expander("📊 Model Information"):
                model_info = st.session_state.current_model.get_model_info()
                for key, value in model_info.items():
                    st.write(f"**{key}:** {value}")
        else:
            st.warning("⚠️ No model loaded")
        
        st.markdown("---")
        
        # Preprocessing options
        st.markdown('<h3 class="sub-header">🔧 Preprocessing Options</h3>', unsafe_allow_html=True)

        apply_skull_stripping = st.checkbox(
            "Apply Skull Stripping",
            value=True,
            help="Remove skull and non-brain tissue (recommended for .nii files)"
        )

        apply_normalization = st.checkbox(
            "Apply Intensity Normalization",
            value=False,
            help="Normalize intensity values to [0,1] range (usually not needed for ncomms2022 model)"
        )
    
    # Main content area
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.markdown('<h2 class="sub-header">📁 MRI Upload & Preprocessing</h2>', unsafe_allow_html=True)
        
        # File upload
        uploaded_file = st.file_uploader(
            "Upload MRI Scan",
            type=['nii', 'npy'],
            help="Supported formats: .nii (NIfTI) or .npy (NumPy array)"
        )
        
        if uploaded_file is not None:
            # Store filename for PDF report
            st.session_state.uploaded_filename = Path(uploaded_file.name).stem

            # Display file info
            file_details = {
                "Filename": uploaded_file.name,
                "File size": f"{uploaded_file.size / 1024 / 1024:.2f} MB",
                "File type": uploaded_file.type or "Unknown"
            }

            st.info("📋 **File Information:**")
            for key, value in file_details.items():
                st.write(f"• **{key}:** {value}")

            # Determine file type
            file_extension = Path(uploaded_file.name).suffix.lower()
            file_type = 'nii' if file_extension in ['.nii', '.nii.gz'] else 'npy'

            # Extract spatial information for high-resolution viewing
            if 'hr_viewer' not in st.session_state:
                st.session_state.hr_viewer = HighResolutionMRIViewer()

            # Extract original spatial info before preprocessing
            with st.spinner("Extracting spatial information..."):
                try:
                    spatial_info = st.session_state.hr_viewer.extract_spatial_info(uploaded_file, file_type)
                    if spatial_info:
                        st.session_state.original_spatial_info = spatial_info

                        # Display original file information
                        st.info(f"📊 **Original Spatial Info**: Shape: {spatial_info['original_shape']} | "
                               f"Voxel Sizes: {[f'{v:.3f}mm' for v in spatial_info['voxel_sizes']]}")

                        # Check for anisotropic voxels
                        voxel_sizes = spatial_info['voxel_sizes']
                        if not all(abs(v - voxel_sizes[0]) < 0.01 for v in voxel_sizes):
                            st.warning("⚠️ **Anisotropic voxels detected** - aspect ratio preservation is critical!")
                        else:
                            st.success("✅ **Isotropic voxels** - uniform spacing detected")
                    else:
                        st.warning("⚠️ Could not extract spatial information - using default settings")
                        st.session_state.original_spatial_info = None
                except Exception as e:
                    st.warning(f"⚠️ Could not extract spatial info: {e}")
                    st.session_state.original_spatial_info = None

            # Preprocessing button
            if st.button("🔄 Preprocess MRI", type="primary"):
                with st.spinner("Preprocessing MRI data..."):
                    # Reset the file pointer
                    uploaded_file.seek(0)
                    
                    # Preprocess the data
                    preprocessed_data = st.session_state.preprocessor.preprocess_mri(
                        uploaded_file,
                        file_type=file_type,
                        apply_skull_stripping=apply_skull_stripping,
                        apply_normalization=apply_normalization
                    )
                    
                    if preprocessed_data is not None:
                        # Validate the data
                        if st.session_state.preprocessor.validate_preprocessed_data(preprocessed_data):
                            st.session_state.preprocessed_data = preprocessed_data
                            st.success("🎉 MRI preprocessing completed successfully!")
                            
                            # Display preprocessing summary
                            st.info("📊 **Preprocessing Summary:**")
                            st.write(f"• **Final shape:** {preprocessed_data.shape}")
                            st.write(f"• **Data type:** {preprocessed_data.dtype}")
                            st.write(f"• **Value range:** [{preprocessed_data.min():.3f}, {preprocessed_data.max():.3f}]")
                            st.write(f"• **Mean intensity:** {preprocessed_data.mean():.3f}")
                        else:
                            st.error("❌ Preprocessing validation failed")
                    else:
                        st.error("❌ Preprocessing failed")
        
        # Display preprocessing status
        if st.session_state.preprocessed_data is not None:
            st.success("✅ MRI data ready for inference")

            # Initialize high-resolution viewer
            if 'hr_viewer' not in st.session_state:
                st.session_state.hr_viewer = HighResolutionMRIViewer()

            # Show sample slices with high-resolution viewer
            with st.expander("👁️ Preview MRI Slices (High Resolution)", expanded=True):
                col1, col2 = st.columns([3, 1])

                with col1:
                    # Check if we have original spatial info
                    if hasattr(st.session_state, 'original_spatial_info') and st.session_state.original_spatial_info:
                        st.info("🎯 **Displaying with original aspect ratios and high resolution**")

                        # Create high-resolution view with proper aspect ratios
                        fig = st.session_state.hr_viewer.create_high_res_orthogonal_view(
                            st.session_state.original_spatial_info,
                            title="High-Resolution MRI Preview"
                        )
                        st.pyplot(fig, use_container_width=True)
                        plt.close(fig)  # Free memory

                    else:
                        st.warning("⚠️ Using processed data - aspect ratios may not be preserved")
                        # Fallback to processed data visualization
                        data = st.session_state.preprocessed_data

                        # Create a simple high-res view for processed data
                        spatial_info = {
                            'data': data,
                            'voxel_sizes': (1.0, 1.0, 1.0),  # Assume isotropic for processed
                            'affine': np.eye(4),
                            'header': None,
                            'original_shape': data.shape
                        }

                        fig = st.session_state.hr_viewer.create_high_res_orthogonal_view(
                            spatial_info,
                            title="High-Resolution MRI Preview (Processed Data)"
                        )
                        st.pyplot(fig, use_container_width=True)
                        plt.close(fig)

                with col2:
                    st.markdown("### 📊 Image Info")
                    if hasattr(st.session_state, 'original_spatial_info') and st.session_state.original_spatial_info:
                        info = st.session_state.original_spatial_info
                        st.metric("Original Shape", f"{info['original_shape']}")
                        voxel_sizes = info['voxel_sizes']
                        st.metric("Voxel Sizes", f"{voxel_sizes[0]:.2f}×{voxel_sizes[1]:.2f}×{voxel_sizes[2]:.2f}mm")

                        # Calculate real-world dimensions
                        real_dims = [info['original_shape'][i] * voxel_sizes[i] for i in range(3)]
                        st.metric("Real Dimensions", f"{real_dims[0]:.0f}×{real_dims[1]:.0f}×{real_dims[2]:.0f}mm")
                    else:
                        data = st.session_state.preprocessed_data
                        st.metric("Processed Shape", f"{data.shape}")
                        st.metric("Voxel Sizes", "1.0×1.0×1.0mm")

                    # High-DPI Zoom Views
                    st.markdown("### 🔍 High-DPI Zoom Views")
                    zoom_col1, zoom_col2, zoom_col3 = st.columns(3)

                    if hasattr(st.session_state, 'original_spatial_info') and st.session_state.original_spatial_info:
                        data = st.session_state.original_spatial_info['data']
                        mid_x, mid_y, mid_z = [dim // 2 for dim in data.shape]

                        with zoom_col1:
                            if st.button("🔍 Zoom Axial", key="zoom_axial_preview"):
                                fig = st.session_state.hr_viewer.create_high_dpi_zoom_view(
                                    st.session_state.original_spatial_info, "axial", mid_z)
                                st.pyplot(fig, use_container_width=True)
                                plt.close(fig)

                        with zoom_col2:
                            if st.button("🔍 Zoom Coronal", key="zoom_coronal_preview"):
                                fig = st.session_state.hr_viewer.create_high_dpi_zoom_view(
                                    st.session_state.original_spatial_info, "coronal", mid_y)
                                st.pyplot(fig, use_container_width=True)
                                plt.close(fig)

                        with zoom_col3:
                            if st.button("🔍 Zoom Sagittal", key="zoom_sagittal_preview"):
                                fig = st.session_state.hr_viewer.create_high_dpi_zoom_view(
                                    st.session_state.original_spatial_info, "sagittal", mid_x)
                                st.pyplot(fig, use_container_width=True)
                                plt.close(fig)

                    # Full-screen viewing button
                    if st.button("🔍 View in Full Screen", key="fullscreen_mri"):
                        if hasattr(st.session_state, 'original_spatial_info') and st.session_state.original_spatial_info:
                            display_fullscreen_mri_with_aspect_ratios(st.session_state.original_spatial_info)
                        else:
                            display_fullscreen_mri(st.session_state.preprocessed_data)
    
    with col2:
        st.markdown('<h2 class="sub-header">🔮 Prediction & Results</h2>', unsafe_allow_html=True)
        
        # Prediction section
        if st.session_state.current_model and st.session_state.preprocessed_data is not None:
            if st.button("🧠 Run Dementia Assessment", type="primary"):
                with st.spinner("Running AI analysis..."):
                    predictions = st.session_state.current_model.predict_single(
                        st.session_state.preprocessed_data
                    )
                    
                    if predictions:
                        st.session_state.predictions = predictions
                        st.success("🎉 Analysis completed!")
                    else:
                        st.error("❌ Prediction failed")
        
        elif not st.session_state.current_model:
            st.warning("⚠️ Please load a model first")
        elif st.session_state.preprocessed_data is None:
            st.warning("⚠️ Please upload and preprocess an MRI scan first")
        
        # Display results
        if st.session_state.predictions:
            # PDF Report Status at the top
            display_pdf_status()

            display_predictions(st.session_state.predictions)

            # SHAP interpretability section
            st.markdown("---")
            st.markdown('<h3 class="sub-header">🔍 Model Interpretability</h3>', unsafe_allow_html=True)

            if st.button("🧠 Generate Brain Region Analysis", help="Generate saliency map showing which brain regions influenced the prediction"):
                with st.spinner("Generating brain region importance analysis..."):
                    try:
                        # Generate SHAP heatmap for ADD task
                        heatmap_data = generate_shap_heatmap(
                            st.session_state.current_model,
                            st.session_state.preprocessed_data,
                            task='ADD'
                        )

                        if heatmap_data is not None:
                            st.session_state.heatmap_data = heatmap_data  # Store for PDF generation

                            # Trigger PDF generation now that heatmap is complete
                            st.session_state.pdf_status = 'not_started'

                            # Generate PDF immediately
                            st.success("✅ Brain region analysis completed! Generating clinical PDF report...")

                            filename_base = st.session_state.get('uploaded_filename', 'scan')

                            try:
                                st.session_state.pdf_status = 'generating'

                                with st.spinner("Generating comprehensive clinical PDF report..."):
                                    pdf_bytes = generate_pdf_report(
                                        st.session_state.preprocessed_data,
                                        st.session_state.heatmap_data,
                                        st.session_state.predictions,
                                        filename_base
                                    )

                                if pdf_bytes:
                                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                                    filename = f"Demetify_Clinical_Report_{filename_base}_{timestamp}.pdf"

                                    # Store in session state
                                    st.session_state.pdf_data = pdf_bytes
                                    st.session_state.pdf_filename = filename
                                    st.session_state.pdf_status = 'ready'

                                    st.success(f"✅ Clinical PDF report ready! ({len(pdf_bytes):,} bytes)")

                                    # Show download button immediately
                                    st.markdown("### 📄 Download Clinical Report")
                                    st.download_button(
                                        label="📄 Download Clinical Report",
                                        data=pdf_bytes,
                                        file_name=filename,
                                        mime="application/pdf",
                                        type="primary",
                                        use_container_width=True,
                                        key="immediate_download"
                                    )
                                    st.info(f"📁 Filename: {filename}")
                                else:
                                    st.session_state.pdf_status = 'error'
                                    st.error("❌ Failed to generate PDF report")

                            except Exception as e:
                                st.session_state.pdf_status = 'error'
                                st.error(f"❌ PDF generation failed: {str(e)}")

                            display_shap_heatmap(heatmap_data, st.session_state.preprocessed_data)
                        else:
                            st.error("❌ Failed to generate brain region analysis")
                    except Exception as e:
                        st.error(f"❌ Error generating brain region analysis: {str(e)}")
                        st.info("💡 Brain region analysis requires additional computational resources and may take several minutes.")

class HighResolutionMRIViewer:
    """High-resolution MRI viewer with proper aspect ratio preservation"""

    def __init__(self):
        self.dpi = 300  # High DPI for medical viewing
        self.figsize_large = (20, 15)  # Large figure size
        self.figsize_fullscreen = (24, 18)  # Full-screen size

    def extract_spatial_info(self, file_input, file_type='nii'):
        """Extract spatial information from NIfTI files"""
        try:
            if file_type == 'nii':
                if isinstance(file_input, str):
                    img = nib.load(file_input)
                else:
                    # Handle uploaded bytes - keep file open during processing
                    tmp_file = tempfile.NamedTemporaryFile(suffix='.nii', delete=False)
                    try:
                        file_input.seek(0)
                        tmp_file.write(file_input.read())
                        tmp_file.flush()
                        tmp_file.close()  # Close file handle but keep file

                        # Load with nibabel while file still exists
                        img = nib.load(tmp_file.name)
                        header = img.header
                        affine = img.affine
                        data = img.get_fdata()
                        voxel_sizes = header.get_zooms()[:3]

                        # Now we can safely delete the temp file
                        os.unlink(tmp_file.name)

                        return {
                            'data': data,
                            'voxel_sizes': voxel_sizes,
                            'affine': affine,
                            'header': header,
                            'original_shape': data.shape
                        }
                    except Exception as e:
                        # Clean up temp file on error
                        if os.path.exists(tmp_file.name):
                            os.unlink(tmp_file.name)
                        raise e

                # For file path case
                header = img.header
                affine = img.affine
                data = img.get_fdata()
                voxel_sizes = header.get_zooms()[:3]

                return {
                    'data': data,
                    'voxel_sizes': voxel_sizes,
                    'affine': affine,
                    'header': header,
                    'original_shape': data.shape
                }
            else:
                # For .npy files
                if isinstance(file_input, str):
                    data = np.load(file_input)
                else:
                    # Handle uploaded .npy file
                    file_input.seek(0)
                    data = np.load(file_input)

                return {
                    'data': data,
                    'voxel_sizes': (1.0, 1.0, 1.0),
                    'affine': np.eye(4),
                    'header': None,
                    'original_shape': data.shape
                }
        except Exception as e:
            st.error(f"Error extracting spatial info: {e}")
            return None

    def calculate_aspect_ratios(self, voxel_sizes):
        """Calculate proper aspect ratios for display"""
        # Use the smallest voxel size as reference
        min_voxel = min(voxel_sizes)
        aspect_ratios = [voxel_sizes[i] / min_voxel for i in range(3)]
        return aspect_ratios

    def apply_clinical_windowing(self, data, window_level=None, window_width=None):
        """Apply clinical windowing for optimal brain tissue contrast"""
        if window_level is None or window_width is None:
            # Auto-calculate brain tissue window
            brain_mask = data > np.percentile(data[data > 0], 5)
            brain_data = data[brain_mask]

            if len(brain_data) > 0:
                window_level = np.percentile(brain_data, 50)
                window_width = np.percentile(brain_data, 95) - np.percentile(brain_data, 5)
            else:
                window_level = np.mean(data)
                window_width = np.std(data) * 4

        # Apply windowing
        min_val = window_level - window_width / 2
        max_val = window_level + window_width / 2
        windowed_data = np.clip(data, min_val, max_val)
        windowed_data = (windowed_data - min_val) / (max_val - min_val)

        return windowed_data

    def create_high_res_orthogonal_view(self, spatial_info, title="High-Resolution MRI Viewer"):
        """Create high-resolution orthogonal view with proper aspect ratios"""
        data = spatial_info['data']
        voxel_sizes = spatial_info['voxel_sizes']
        aspect_ratios = self.calculate_aspect_ratios(voxel_sizes)

        # Apply clinical windowing
        windowed_data = self.apply_clinical_windowing(data)

        # Get middle slices
        mid_x, mid_y, mid_z = [dim // 2 for dim in data.shape]

        # Create high-resolution figure
        fig, axes = plt.subplots(2, 3, figsize=self.figsize_large, dpi=self.dpi)
        fig.suptitle(f"{title}\nOriginal Shape: {data.shape} | Voxel Sizes: {[f'{v:.3f}mm' for v in voxel_sizes]}",
                     fontsize=16, fontweight='bold')

        # Radiological orientations (Bottom to Top, Left to Right, Anterior to Posterior)

        # Row 1: Primary orthogonal views
        # Axial view (Bottom to Top) - XY plane
        axial_slice = windowed_data[:, :, mid_z]
        im1 = axes[0, 0].imshow(axial_slice, cmap='gray',
                               aspect=aspect_ratios[1]/aspect_ratios[0],  # Y/X ratio
                               origin='lower', vmin=0, vmax=1)
        axes[0, 0].set_title(f'Axial (Bottom→Top)\nZ={mid_z}', fontweight='bold', color='#2E86AB')
        axes[0, 0].axis('off')

        # Coronal view (Anterior to Posterior) - XZ plane
        coronal_slice = windowed_data[:, mid_y, :]
        im2 = axes[0, 1].imshow(coronal_slice, cmap='gray',
                               aspect=aspect_ratios[2]/aspect_ratios[0],  # Z/X ratio
                               origin='lower', vmin=0, vmax=1)
        axes[0, 1].set_title(f'Coronal (Anterior→Posterior)\nY={mid_y}', fontweight='bold', color='#2E86AB')
        axes[0, 1].axis('off')

        # Sagittal view (Left to Right) - YZ plane
        sagittal_slice = windowed_data[mid_x, :, :]
        im3 = axes[0, 2].imshow(sagittal_slice, cmap='gray',
                               aspect=aspect_ratios[2]/aspect_ratios[1],  # Z/Y ratio
                               origin='lower', vmin=0, vmax=1)
        axes[0, 2].set_title(f'Sagittal (Left→Right)\nX={mid_x}', fontweight='bold', color='#2E86AB')
        axes[0, 2].axis('off')

        # Row 2: Additional offset views for better anatomy visualization
        offset = max(5, min(data.shape) // 20)

        # Axial offset
        axial_offset = min(mid_z + offset, data.shape[2] - 1)
        axial_slice2 = windowed_data[:, :, axial_offset]
        im4 = axes[1, 0].imshow(axial_slice2, cmap='gray',
                               aspect=aspect_ratios[1]/aspect_ratios[0],
                               origin='lower', vmin=0, vmax=1)
        axes[1, 0].set_title(f'Axial +{offset}\nZ={axial_offset}', fontweight='bold', color='#A23B72')
        axes[1, 0].axis('off')

        # Coronal offset
        coronal_offset = min(mid_y + offset, data.shape[1] - 1)
        coronal_slice2 = windowed_data[:, coronal_offset, :]
        im5 = axes[1, 1].imshow(coronal_slice2, cmap='gray',
                               aspect=aspect_ratios[2]/aspect_ratios[0],
                               origin='lower', vmin=0, vmax=1)
        axes[1, 1].set_title(f'Coronal +{offset}\nY={coronal_offset}', fontweight='bold', color='#A23B72')
        axes[1, 1].axis('off')

        # Sagittal offset
        sagittal_offset = min(mid_x + offset, data.shape[0] - 1)
        sagittal_slice2 = windowed_data[sagittal_offset, :, :]
        im6 = axes[1, 2].imshow(sagittal_slice2, cmap='gray',
                               aspect=aspect_ratios[2]/aspect_ratios[1],
                               origin='lower', vmin=0, vmax=1)
        axes[1, 2].set_title(f'Sagittal +{offset}\nX={sagittal_offset}', fontweight='bold', color='#A23B72')
        axes[1, 2].axis('off')

        # Add colorbar
        cbar = plt.colorbar(im1, ax=axes[0, 0], fraction=0.046, pad=0.04)
        cbar.set_label('Normalized Intensity', rotation=270, labelpad=15)

        plt.tight_layout()
        return fig

    def create_high_dpi_zoom_view(self, spatial_info, view_type, slice_idx):
        """Create a high-DPI zoomed view using matplotlib for better quality"""
        data = spatial_info['data']
        voxel_sizes = spatial_info['voxel_sizes']
        aspect_ratios = self.calculate_aspect_ratios(voxel_sizes)
        windowed_data = self.apply_clinical_windowing(data)

        # Create high-DPI figure
        fig, ax = plt.subplots(1, 1, figsize=(12, 10), dpi=self.dpi)

        if view_type == "axial":
            slice_data = windowed_data[:, :, slice_idx]
            aspect_ratio = aspect_ratios[1] / aspect_ratios[0]  # Y/X ratio
            title = f"High-DPI Axial View (Bottom→Top) - Slice {slice_idx}"
            xlabel = f"X-axis ({data.shape[0]} pixels, {data.shape[0] * voxel_sizes[0]:.1f}mm)"
            ylabel = f"Y-axis ({data.shape[1]} pixels, {data.shape[1] * voxel_sizes[1]:.1f}mm)"

        elif view_type == "coronal":
            slice_data = windowed_data[:, slice_idx, :]
            aspect_ratio = aspect_ratios[2] / aspect_ratios[0]  # Z/X ratio
            title = f"High-DPI Coronal View (Anterior→Posterior) - Slice {slice_idx}"
            xlabel = f"X-axis ({data.shape[0]} pixels, {data.shape[0] * voxel_sizes[0]:.1f}mm)"
            ylabel = f"Z-axis ({data.shape[2]} pixels, {data.shape[2] * voxel_sizes[2]:.1f}mm)"

        else:  # sagittal
            slice_data = windowed_data[slice_idx, :, :]
            aspect_ratio = aspect_ratios[2] / aspect_ratios[1]  # Z/Y ratio
            title = f"High-DPI Sagittal View (Left→Right) - Slice {slice_idx}"
            xlabel = f"Y-axis ({data.shape[1]} pixels, {data.shape[1] * voxel_sizes[1]:.1f}mm)"
            ylabel = f"Z-axis ({data.shape[2]} pixels, {data.shape[2] * voxel_sizes[2]:.1f}mm)"

        # Display with proper aspect ratio
        im = ax.imshow(slice_data, cmap='gray', aspect=aspect_ratio, origin='lower', vmin=0, vmax=1)

        # Add title and labels
        ax.set_title(title, fontsize=16, fontweight='bold', pad=20)
        ax.set_xlabel(xlabel, fontsize=12)
        ax.set_ylabel(ylabel, fontsize=12)

        # Add colorbar
        cbar = plt.colorbar(im, ax=ax, fraction=0.046, pad=0.04)
        cbar.set_label('Normalized Intensity', rotation=270, labelpad=15, fontsize=12)

        # Add aspect ratio info
        ax.text(0.02, 0.98, f'Aspect Ratio: {aspect_ratio:.3f}',
                transform=ax.transAxes, fontsize=10, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        # Add voxel size info
        voxel_info = f'Voxel Sizes: {voxel_sizes[0]:.3f}×{voxel_sizes[1]:.3f}×{voxel_sizes[2]:.3f}mm'
        ax.text(0.02, 0.02, voxel_info,
                transform=ax.transAxes, fontsize=10, verticalalignment='bottom',
                bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

        plt.tight_layout()
        return fig

def display_fullscreen_mri_with_aspect_ratios(spatial_info):
    """Display full-screen MRI viewer with proper aspect ratios"""
    st.markdown("## 🔍 Full-Screen High-Resolution MRI Viewer")
    st.info("🎯 **Displaying with original aspect ratios and anatomical orientations**")

    data = spatial_info['data']
    voxel_sizes = spatial_info['voxel_sizes']

    # Initialize viewer
    if 'hr_viewer' not in st.session_state:
        st.session_state.hr_viewer = HighResolutionMRIViewer()

    # Slice selection controls
    col1, col2, col3, col4 = st.columns(4)

    with col1:
        axial_slice = st.slider(
            "Axial Slice (Bottom→Top)",
            0, data.shape[2]-1,
            data.shape[2]//2,
            key="fs_axial_slider"
        )

    with col2:
        coronal_slice = st.slider(
            "Coronal Slice (Ant→Post)",
            0, data.shape[1]-1,
            data.shape[1]//2,
            key="fs_coronal_slider"
        )

    with col3:
        sagittal_slice = st.slider(
            "Sagittal Slice (Left→Right)",
            0, data.shape[0]-1,
            data.shape[0]//2,
            key="fs_sagittal_slider"
        )

    with col4:
        view_mode = st.selectbox(
            "View Mode",
            ["All Views", "Axial Only", "Coronal Only", "Sagittal Only"],
            key="fs_view_mode"
        )

    # Apply clinical windowing
    windowed_data = st.session_state.hr_viewer.apply_clinical_windowing(data)
    aspect_ratios = st.session_state.hr_viewer.calculate_aspect_ratios(voxel_sizes)

    # Create high-resolution display based on view mode
    if view_mode == "All Views":
        # Create comprehensive view
        fig, axes = plt.subplots(2, 2, figsize=(20, 16), dpi=300)
        fig.suptitle(f"High-Resolution MRI Viewer\nShape: {data.shape} | Voxel: {[f'{v:.3f}mm' for v in voxel_sizes]}",
                     fontsize=18, fontweight='bold')

        # Axial view
        axial_data = windowed_data[:, :, axial_slice]
        axes[0, 0].imshow(axial_data, cmap='gray', aspect=aspect_ratios[1]/aspect_ratios[0],
                         origin='lower', vmin=0, vmax=1)
        axes[0, 0].set_title(f'Axial (Bottom→Top) - Slice {axial_slice}', fontweight='bold', fontsize=14)
        axes[0, 0].axis('off')

        # Coronal view
        coronal_data = windowed_data[:, coronal_slice, :]
        axes[0, 1].imshow(coronal_data, cmap='gray', aspect=aspect_ratios[2]/aspect_ratios[0],
                         origin='lower', vmin=0, vmax=1)
        axes[0, 1].set_title(f'Coronal (Anterior→Posterior) - Slice {coronal_slice}', fontweight='bold', fontsize=14)
        axes[0, 1].axis('off')

        # Sagittal view
        sagittal_data = windowed_data[sagittal_slice, :, :]
        axes[1, 0].imshow(sagittal_data, cmap='gray', aspect=aspect_ratios[2]/aspect_ratios[1],
                         origin='lower', vmin=0, vmax=1)
        axes[1, 0].set_title(f'Sagittal (Left→Right) - Slice {sagittal_slice}', fontweight='bold', fontsize=14)
        axes[1, 0].axis('off')

        # Combined crosshair view
        combined_data = windowed_data[:, :, axial_slice].copy()
        # Add crosshairs
        if 0 <= coronal_slice < combined_data.shape[1]:
            combined_data[:, coronal_slice] = 1.0  # Vertical line
        if 0 <= sagittal_slice < combined_data.shape[0]:
            combined_data[sagittal_slice, :] = 1.0  # Horizontal line

        axes[1, 1].imshow(combined_data, cmap='gray', aspect=aspect_ratios[1]/aspect_ratios[0],
                         origin='lower', vmin=0, vmax=1)
        axes[1, 1].set_title('Axial with Crosshairs', fontweight='bold', fontsize=14)
        axes[1, 1].axis('off')

    else:
        # Single view mode
        fig, ax = plt.subplots(1, 1, figsize=(16, 12), dpi=300)

        if view_mode == "Axial Only":
            slice_data = windowed_data[:, :, axial_slice]
            aspect = aspect_ratios[1]/aspect_ratios[0]
            title = f'Axial View (Bottom→Top) - Slice {axial_slice}'
        elif view_mode == "Coronal Only":
            slice_data = windowed_data[:, coronal_slice, :]
            aspect = aspect_ratios[2]/aspect_ratios[0]
            title = f'Coronal View (Anterior→Posterior) - Slice {coronal_slice}'
        else:  # Sagittal Only
            slice_data = windowed_data[sagittal_slice, :, :]
            aspect = aspect_ratios[2]/aspect_ratios[1]
            title = f'Sagittal View (Left→Right) - Slice {sagittal_slice}'

        ax.imshow(slice_data, cmap='gray', aspect=aspect, origin='lower', vmin=0, vmax=1)
        ax.set_title(title, fontweight='bold', fontsize=16)
        ax.axis('off')

    plt.tight_layout()
    st.pyplot(fig, use_container_width=True)
    plt.close(fig)

    # Display spatial information
    st.markdown("### 📊 Spatial Information")
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Original Shape", f"{data.shape}")
    with col2:
        st.metric("Voxel Sizes", f"{voxel_sizes[0]:.3f}×{voxel_sizes[1]:.3f}×{voxel_sizes[2]:.3f}mm")
    with col3:
        real_dims = [data.shape[i] * voxel_sizes[i] for i in range(3)]
        st.metric("Real Dimensions", f"{real_dims[0]:.0f}×{real_dims[1]:.0f}×{real_dims[2]:.0f}mm")
    with col4:
        st.metric("Aspect Ratios", f"{aspect_ratios[0]:.2f}:{aspect_ratios[1]:.2f}:{aspect_ratios[2]:.2f}")

def display_fullscreen_mri(mri_data):
    """
    Display MRI data in full-screen mode with interactive controls.

    Args:
        mri_data: 3D MRI data array
    """
    st.markdown("## 🔍 Full-Screen MRI Viewer")

    # Slice selection controls
    col1, col2, col3 = st.columns(3)

    with col1:
        axial_slice = st.slider(
            "Axial Slice",
            0, mri_data.shape[2]-1,
            mri_data.shape[2]//2,
            key="axial_slider"
        )

    with col2:
        coronal_slice = st.slider(
            "Coronal Slice",
            0, mri_data.shape[1]-1,
            mri_data.shape[1]//2,
            key="coronal_slider"
        )

    with col3:
        sagittal_slice = st.slider(
            "Sagittal Slice",
            0, mri_data.shape[0]-1,
            mri_data.shape[0]//2,
            key="sagittal_slider"
        )

    # Display controls
    col1, col2 = st.columns(2)
    with col1:
        colorscale = st.selectbox(
            "Color Scale",
            ["gray", "viridis", "plasma", "inferno", "magma", "bone"],
            index=0
        )

    with col2:
        show_colorbar = st.checkbox("Show Color Bar", value=True)

    # Create full-screen plots
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=[
            f"Axial (Z={axial_slice})",
            f"Coronal (Y={coronal_slice})",
            f"Sagittal (X={sagittal_slice})",
            "3D Cross-section View"
        ],
        specs=[[{"type": "heatmap"}, {"type": "heatmap"}],
               [{"type": "heatmap"}, {"type": "heatmap"}]]
    )

    # Axial view
    axial_data = mri_data[:, :, axial_slice]
    fig.add_trace(
        go.Heatmap(
            z=axial_data,
            colorscale=colorscale,
            showscale=show_colorbar,
            colorbar=dict(title="Intensity", x=0.48, len=0.4) if show_colorbar else None
        ),
        row=1, col=1
    )

    # Coronal view
    coronal_data = mri_data[:, coronal_slice, :]
    fig.add_trace(
        go.Heatmap(
            z=coronal_data,
            colorscale=colorscale,
            showscale=False
        ),
        row=1, col=2
    )

    # Sagittal view
    sagittal_data = mri_data[sagittal_slice, :, :]
    fig.add_trace(
        go.Heatmap(
            z=sagittal_data,
            colorscale=colorscale,
            showscale=False
        ),
        row=2, col=1
    )

    # Combined view showing intersection
    combined_data = np.zeros_like(axial_data)
    combined_data[:, :] = axial_data
    # Add crosshairs
    combined_data[coronal_slice, :] = np.max(axial_data)  # Horizontal line
    combined_data[:, sagittal_slice] = np.max(axial_data)  # Vertical line

    fig.add_trace(
        go.Heatmap(
            z=combined_data,
            colorscale=colorscale,
            showscale=False
        ),
        row=2, col=2
    )

    fig.update_layout(
        height=800,
        showlegend=False,
        title_text="Interactive MRI Viewer - Navigate with sliders above"
    )
    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    st.plotly_chart(fig, use_container_width=True)

    # Data information
    st.markdown("### 📊 Data Information")
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Shape", f"{mri_data.shape}")
    with col2:
        st.metric("Min Value", f"{mri_data.min():.3f}")
    with col3:
        st.metric("Max Value", f"{mri_data.max():.3f}")
    with col4:
        st.metric("Mean Value", f"{mri_data.mean():.3f}")

def generate_shap_heatmap(model, mri_data, task='ADD'):
    """
    Generate gradient-based saliency map for model interpretability.

    Args:
        model: Loaded NCOMMs2022Model instance
        mri_data: Preprocessed MRI data
        task: Task to analyze ('ADD' or 'COG')

    Returns:
        numpy array: Saliency values for visualization
    """
    try:
        import torch

        # Prepare input for gradient analysis
        input_tensor = torch.from_numpy(mri_data).float()
        input_tensor = input_tensor.unsqueeze(0).unsqueeze(0)  # Add batch and channel dimensions
        input_tensor = input_tensor.to(model.device)
        input_tensor.requires_grad_(True)

        # Forward pass
        model.model.eval()  # Ensure model is in eval mode
        predictions = model.model(input_tensor)

        # Select target output based on task
        if task == 'ADD':
            # Use the predicted class probability
            add_probs = torch.softmax(predictions['ADD'], dim=1)
            target_output = add_probs[:, 1]  # AD probability
        else:
            target_output = predictions['COG'][:, 0]

        # Compute gradients
        target_output.backward()
        gradients = input_tensor.grad

        if gradients is None:
            st.error("❌ Failed to compute gradients")
            return None

        # Use gradient magnitude as saliency map
        saliency_map = torch.abs(gradients[0, 0]).cpu().numpy()

        # Apply smoothing to reduce noise
        from scipy.ndimage import gaussian_filter
        saliency_map = gaussian_filter(saliency_map, sigma=1.0)

        st.success("✅ Brain region importance analysis completed!")
        return saliency_map

    except Exception as e:
        st.error(f"❌ Error generating brain region analysis: {str(e)}")
        import traceback
        st.error(f"Details: {traceback.format_exc()}")
        return None

def create_high_dpi_heatmap_zoom(heatmap_data, original_data, view_type, slice_idx, colorscale='hot'):
    """Create a high-DPI zoomed heatmap view using matplotlib"""

    # Normalize heatmap for visualization
    heatmap_norm = (heatmap_data - heatmap_data.min()) / (heatmap_data.max() - heatmap_data.min())

    # Create high-DPI figure with side-by-side comparison
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8), dpi=300)

    if view_type == "axial":
        original_slice = original_data[:, :, slice_idx]
        heatmap_slice = heatmap_norm[:, :, slice_idx]
        title_base = f"High-DPI Axial View (Bottom→Top) - Slice {slice_idx}"

    elif view_type == "coronal":
        original_slice = original_data[:, slice_idx, :]
        heatmap_slice = heatmap_norm[:, slice_idx, :]
        title_base = f"High-DPI Coronal View (Anterior→Posterior) - Slice {slice_idx}"

    else:  # sagittal
        original_slice = original_data[slice_idx, :, :]
        heatmap_slice = heatmap_norm[slice_idx, :, :]
        title_base = f"High-DPI Sagittal View (Left→Right) - Slice {slice_idx}"

    # Display original MRI
    ax1.imshow(original_slice, cmap='gray', origin='lower')
    ax1.set_title(f"{title_base}\nOriginal MRI", fontsize=14, fontweight='bold')
    ax1.axis('off')

    # Display heatmap overlay
    im2 = ax2.imshow(original_slice, cmap='gray', origin='lower', alpha=0.7)
    im_heatmap = ax2.imshow(heatmap_slice, cmap=colorscale, origin='lower', alpha=0.8)
    ax2.set_title(f"{title_base}\nAI Attention Heatmap", fontsize=14, fontweight='bold')
    ax2.axis('off')

    # Add colorbar for heatmap
    cbar = plt.colorbar(im_heatmap, ax=ax2, fraction=0.046, pad=0.04)
    cbar.set_label('AI Attention Score', rotation=270, labelpad=15, fontsize=12)

    # Add statistics
    stats_text = f'Max: {heatmap_data.max():.6f} | Mean: {heatmap_data.mean():.6f} | Std: {heatmap_data.std():.6f}'
    fig.suptitle(f"{title_base}\n{stats_text}", fontsize=16, fontweight='bold')

    plt.tight_layout()

    # Display in Streamlit
    st.pyplot(fig, use_container_width=True)
    plt.close(fig)  # Free memory

    # Add interpretation
    st.info("🔍 **Interpretation**: Bright/hot colors show brain regions that most influenced the AI's decision. The left image shows the original MRI, the right shows AI attention overlay.")

def display_shap_heatmap(heatmap_data, original_data):
    """
    Display SHAP heatmap overlaid on original MRI data.

    Args:
        heatmap_data: SHAP values or saliency map
        original_data: Original MRI data
    """
    st.markdown("### 🔥 Brain Region Importance Heatmap")
    st.info("This heatmap shows which brain regions most influenced the AI's decision. Brighter areas had more impact on the prediction.")

    # Normalize heatmap for visualization
    heatmap_norm = (heatmap_data - heatmap_data.min()) / (heatmap_data.max() - heatmap_data.min())

    # Color scale selection
    col1, col2 = st.columns(2)
    with col1:
        heatmap_colorscale = st.selectbox(
            "Heatmap Color Scale",
            ["hot", "viridis", "plasma", "inferno", "magma", "turbo", "jet"],
            index=0,
            key="heatmap_colorscale"
        )
    with col2:
        show_colorbar = st.checkbox("Show Color Bar", value=True, key="heatmap_colorbar")

    # Create overlay visualization
    fig = make_subplots(
        rows=2, cols=3,
        subplot_titles=["Axial - Original", "Coronal - Original", "Sagittal - Original",
                       "Axial - Heatmap", "Coronal - Heatmap", "Sagittal - Heatmap"],
        vertical_spacing=0.1,
        horizontal_spacing=0.05
    )

    # Original slices (top row)
    mid_x, mid_y, mid_z = original_data.shape[0]//2, original_data.shape[1]//2, original_data.shape[2]//2

    # Axial slice
    fig.add_trace(
        go.Heatmap(z=original_data[:, :, mid_z], colorscale='gray', showscale=False),
        row=1, col=1
    )

    # Coronal slice
    fig.add_trace(
        go.Heatmap(z=original_data[:, mid_y, :], colorscale='gray', showscale=False),
        row=1, col=2
    )

    # Sagittal slice
    fig.add_trace(
        go.Heatmap(z=original_data[mid_x, :, :], colorscale='gray', showscale=False),
        row=1, col=3
    )

    # Heatmap slices (bottom row)
    # Axial heatmap
    fig.add_trace(
        go.Heatmap(
            z=heatmap_norm[:, :, mid_z],
            colorscale=heatmap_colorscale,
            showscale=show_colorbar,
            colorbar=dict(title="Importance", x=1.02, len=0.4) if show_colorbar else None
        ),
        row=2, col=1
    )

    # Coronal heatmap
    fig.add_trace(
        go.Heatmap(z=heatmap_norm[:, mid_y, :], colorscale=heatmap_colorscale, showscale=False),
        row=2, col=2
    )

    # Sagittal heatmap
    fig.add_trace(
        go.Heatmap(z=heatmap_norm[mid_x, :, :], colorscale=heatmap_colorscale, showscale=False),
        row=2, col=3
    )

    fig.update_layout(
        height=800,  # Increased height for bigger display
        showlegend=False,
        title_text="Brain Region Importance Analysis"
    )
    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    st.plotly_chart(fig, use_container_width=True)

    # High-DPI Heatmap Zoom Views
    st.markdown("### 🔍 High-DPI Heatmap Zoom Views")
    heatmap_zoom_col1, heatmap_zoom_col2, heatmap_zoom_col3 = st.columns(3)

    mid_x, mid_y, mid_z = original_data.shape[0]//2, original_data.shape[1]//2, original_data.shape[2]//2

    with heatmap_zoom_col1:
        if st.button("🔥 Zoom Axial Heatmap", key="zoom_axial_heatmap"):
            create_high_dpi_heatmap_zoom(heatmap_data, original_data, "axial", mid_z, heatmap_colorscale)

    with heatmap_zoom_col2:
        if st.button("🔥 Zoom Coronal Heatmap", key="zoom_coronal_heatmap"):
            create_high_dpi_heatmap_zoom(heatmap_data, original_data, "coronal", mid_y, heatmap_colorscale)

    with heatmap_zoom_col3:
        if st.button("🔥 Zoom Sagittal Heatmap", key="zoom_sagittal_heatmap"):
            create_high_dpi_heatmap_zoom(heatmap_data, original_data, "sagittal", mid_x, heatmap_colorscale)

    # Full-screen heatmap viewing
    if st.button("🔍 View Heatmap in Full Screen", key="fullscreen_heatmap"):
        st.session_state.show_fullscreen_heatmap = True
        st.session_state.fullscreen_heatmap_data = heatmap_data
        st.session_state.fullscreen_original_data = original_data
        st.session_state.fullscreen_colorscale = heatmap_colorscale

    # Display fullscreen heatmap if requested
    if st.session_state.get('show_fullscreen_heatmap', False):
        display_fullscreen_heatmap(
            st.session_state.fullscreen_heatmap_data,
            st.session_state.fullscreen_original_data,
            st.session_state.fullscreen_colorscale
        )
        if st.button("❌ Close Full Screen", key="close_fullscreen"):
            st.session_state.show_fullscreen_heatmap = False

    # Add interpretation guide
    st.markdown("""
    **🔍 How to interpret this heatmap:**
    - **Bright/Hot colors**: Brain regions that strongly influenced the prediction
    - **Dark/Cool colors**: Brain regions with minimal influence
    - **Top row**: Original MRI slices for reference
    - **Bottom row**: Importance heatmap showing AI attention
    """)

def display_fullscreen_heatmap(heatmap_data, original_data, colorscale='hot'):
    """
    Display heatmap in full-screen mode with interactive controls.

    Args:
        heatmap_data: SHAP values or saliency map
        original_data: Original MRI data
        colorscale: Color scale for heatmap
    """
    st.markdown("## 🔥 Full-Screen Heatmap Viewer")

    # Normalize heatmap for visualization
    heatmap_norm = (heatmap_data - heatmap_data.min()) / (heatmap_data.max() - heatmap_data.min())

    # Slice selection controls
    col1, col2, col3 = st.columns(3)

    with col1:
        axial_slice = st.slider(
            "Axial Slice",
            0, heatmap_data.shape[2]-1,
            heatmap_data.shape[2]//2,
            key="heatmap_axial_slider"
        )

    with col2:
        coronal_slice = st.slider(
            "Coronal Slice",
            0, heatmap_data.shape[1]-1,
            heatmap_data.shape[1]//2,
            key="heatmap_coronal_slider"
        )

    with col3:
        sagittal_slice = st.slider(
            "Sagittal Slice",
            0, heatmap_data.shape[0]-1,
            heatmap_data.shape[0]//2,
            key="heatmap_sagittal_slider"
        )

    # Display controls
    col1, col2, col3 = st.columns(3)
    with col1:
        heatmap_colorscale = st.selectbox(
            "Heatmap Color Scale",
            ["hot", "viridis", "plasma", "inferno", "magma", "turbo", "jet"],
            index=0 if colorscale == 'hot' else ["hot", "viridis", "plasma", "inferno", "magma", "turbo", "jet"].index(colorscale),
            key="fullscreen_heatmap_colorscale"
        )

    with col2:
        overlay_alpha = st.slider("Overlay Transparency", 0.0, 1.0, 0.7, key="overlay_alpha")

    with col3:
        view_mode = st.selectbox(
            "View Mode",
            ["Side by Side", "Overlay", "Heatmap Only"],
            key="view_mode"
        )

    if view_mode == "Side by Side":
        # Side by side view
        fig = make_subplots(
            rows=2, cols=3,
            subplot_titles=[
                f"Axial Original (Z={axial_slice})",
                f"Coronal Original (Y={coronal_slice})",
                f"Sagittal Original (X={sagittal_slice})",
                f"Axial Heatmap (Z={axial_slice})",
                f"Coronal Heatmap (Y={coronal_slice})",
                f"Sagittal Heatmap (X={sagittal_slice})"
            ]
        )

        # Original images (top row)
        fig.add_trace(
            go.Heatmap(z=original_data[:, :, axial_slice], colorscale='gray', showscale=False),
            row=1, col=1
        )
        fig.add_trace(
            go.Heatmap(z=original_data[:, coronal_slice, :], colorscale='gray', showscale=False),
            row=1, col=2
        )
        fig.add_trace(
            go.Heatmap(z=original_data[sagittal_slice, :, :], colorscale='gray', showscale=False),
            row=1, col=3
        )

        # Heatmaps (bottom row)
        fig.add_trace(
            go.Heatmap(
                z=heatmap_norm[:, :, axial_slice],
                colorscale=heatmap_colorscale,
                showscale=True,
                colorbar=dict(title="Importance", x=1.02, len=0.4)
            ),
            row=2, col=1
        )
        fig.add_trace(
            go.Heatmap(z=heatmap_norm[:, coronal_slice, :], colorscale=heatmap_colorscale, showscale=False),
            row=2, col=2
        )
        fig.add_trace(
            go.Heatmap(z=heatmap_norm[sagittal_slice, :, :], colorscale=heatmap_colorscale, showscale=False),
            row=2, col=3
        )

    elif view_mode == "Heatmap Only":
        # Heatmap only view
        fig = make_subplots(
            rows=1, cols=3,
            subplot_titles=[
                f"Axial Heatmap (Z={axial_slice})",
                f"Coronal Heatmap (Y={coronal_slice})",
                f"Sagittal Heatmap (X={sagittal_slice})"
            ]
        )

        fig.add_trace(
            go.Heatmap(
                z=heatmap_norm[:, :, axial_slice],
                colorscale=heatmap_colorscale,
                showscale=True,
                colorbar=dict(title="Importance", x=1.02, len=0.8)
            ),
            row=1, col=1
        )
        fig.add_trace(
            go.Heatmap(z=heatmap_norm[:, coronal_slice, :], colorscale=heatmap_colorscale, showscale=False),
            row=1, col=2
        )
        fig.add_trace(
            go.Heatmap(z=heatmap_norm[sagittal_slice, :, :], colorscale=heatmap_colorscale, showscale=False),
            row=1, col=3
        )

    else:  # Overlay mode
        # Create overlay by blending original and heatmap
        fig = make_subplots(
            rows=1, cols=3,
            subplot_titles=[
                f"Axial Overlay (Z={axial_slice})",
                f"Coronal Overlay (Y={coronal_slice})",
                f"Sagittal Overlay (X={sagittal_slice})"
            ]
        )

        # Create overlays
        axial_overlay = original_data[:, :, axial_slice] * (1 - overlay_alpha) + heatmap_norm[:, :, axial_slice] * overlay_alpha
        coronal_overlay = original_data[:, coronal_slice, :] * (1 - overlay_alpha) + heatmap_norm[:, coronal_slice, :] * overlay_alpha
        sagittal_overlay = original_data[sagittal_slice, :, :] * (1 - overlay_alpha) + heatmap_norm[sagittal_slice, :, :] * overlay_alpha

        fig.add_trace(
            go.Heatmap(
                z=axial_overlay,
                colorscale=heatmap_colorscale,
                showscale=True,
                colorbar=dict(title="Blended", x=1.02, len=0.8)
            ),
            row=1, col=1
        )
        fig.add_trace(
            go.Heatmap(z=coronal_overlay, colorscale=heatmap_colorscale, showscale=False),
            row=1, col=2
        )
        fig.add_trace(
            go.Heatmap(z=sagittal_overlay, colorscale=heatmap_colorscale, showscale=False),
            row=1, col=3
        )

    fig.update_layout(
        height=800 if view_mode == "Side by Side" else 400,
        showlegend=False,
        title_text=f"Interactive Heatmap Viewer - {view_mode} Mode"
    )
    fig.update_xaxes(showticklabels=False)
    fig.update_yaxes(showticklabels=False)

    st.plotly_chart(fig, use_container_width=True)

    # Heatmap statistics
    st.markdown("### 📊 Heatmap Statistics")
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Min Importance", f"{heatmap_data.min():.6f}")
    with col2:
        st.metric("Max Importance", f"{heatmap_data.max():.6f}")
    with col3:
        st.metric("Mean Importance", f"{heatmap_data.mean():.6f}")
    with col4:
        st.metric("Std Importance", f"{heatmap_data.std():.6f}")

def display_pdf_status():
    """
    Display PDF generation status and download button at the top of results.
    """
    st.markdown("### 📄 Clinical PDF Report")

    # Debug information (remove in production)
    with st.expander("🔍 Debug Info", expanded=False):
        st.write(f"PDF Status: {st.session_state.get('pdf_status', 'not_set')}")
        st.write(f"Has heatmap: {'heatmap_data' in st.session_state}")
        st.write(f"Has PDF data: {st.session_state.get('pdf_data') is not None}")
        st.write(f"Has PDF filename: {st.session_state.get('pdf_filename') is not None}")
        if 'pdf_filename' in st.session_state:
            st.write(f"PDF filename: {st.session_state.pdf_filename}")
        if 'pdf_data' in st.session_state and st.session_state.pdf_data:
            st.write(f"PDF size: {len(st.session_state.pdf_data)} bytes")

    # Check if all required components are available
    has_heatmap = 'heatmap_data' in st.session_state

    if not has_heatmap:
        # Show waiting for heatmap status
        st.warning("⏳ **Status**: Waiting for brain region analysis to complete...")
        st.info("💡 **Note**: Complete clinical report requires brain region importance analysis. Please generate heatmap first.")

    elif st.session_state.get('pdf_status') == 'ready':
        # Show download button prominently when ready
        st.success("✅ **Status**: Complete clinical report ready for download!")

        # Check if we have the PDF data
        pdf_data = st.session_state.get('pdf_data')
        pdf_filename = st.session_state.get('pdf_filename')

        if pdf_data and pdf_filename:
            # Make download button prominent and always visible
            col1, col2, col3 = st.columns([1, 2, 1])
            with col2:
                st.download_button(
                    label="📄 Download Clinical Report",
                    data=pdf_data,
                    file_name=pdf_filename,
                    mime="application/pdf",
                    type="primary",
                    use_container_width=True,
                    key="top_download_clinical_pdf"
                )

            # Also show a direct download link as backup
            st.markdown("---")
            st.markdown("**Alternative Download Options:**")

            col1, col2 = st.columns(2)
            with col1:
                st.download_button(
                    label="💾 Download PDF (Alternative)",
                    data=pdf_data,
                    file_name=pdf_filename,
                    mime="application/pdf",
                    key="alt_download_pdf"
                )
            with col2:
                st.info(f"📊 Size: {len(pdf_data):,} bytes")

            st.code(f"Filename: {pdf_filename}", language=None)
        else:
            st.error("❌ PDF data not available. Please regenerate.")
            st.write(f"Debug: pdf_data exists: {pdf_data is not None}, pdf_filename exists: {pdf_filename is not None}")

    elif st.session_state.get('pdf_status') == 'generating':
        st.info("⏳ **Status**: Generating comprehensive clinical report...")

    elif st.session_state.get('pdf_status') == 'error':
        st.error("❌ **Status**: PDF generation failed")
        if st.button("🔄 Retry PDF Generation", key="pdf_retry"):
            st.session_state.pdf_status = 'not_started'
            st.rerun()

    else:
        st.info("🔄 **Status**: Ready to generate clinical report after brain analysis...")

    st.markdown("---")

def start_background_pdf_generation():
    """
    Start background PDF generation only after heatmap analysis is complete.
    """
    # Only generate PDF when we have predictions, preprocessed data, AND heatmap data
    if ('predictions' in st.session_state and
        'preprocessed_data' in st.session_state and
        'heatmap_data' in st.session_state and
        st.session_state.pdf_status == 'not_started'):

        st.session_state.pdf_status = 'generating'

        # Use a placeholder to show we're generating
        with st.spinner("Generating comprehensive PDF report with brain region analysis..."):
            # Generate PDF in background
            filename_base = st.session_state.get('uploaded_filename', 'scan')

            try:
                pdf_bytes = generate_pdf_report(
                    st.session_state.preprocessed_data,
                    st.session_state.heatmap_data,  # Now guaranteed to exist
                    st.session_state.predictions,
                    filename_base
                )

                if pdf_bytes:
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    filename = f"Demetify_Clinical_Report_{filename_base}_{timestamp}.pdf"

                    st.session_state.pdf_data = pdf_bytes
                    st.session_state.pdf_filename = filename
                    st.session_state.pdf_status = 'ready'
                else:
                    st.session_state.pdf_status = 'error'

            except Exception as e:
                st.session_state.pdf_status = 'error'
                st.error(f"Background PDF generation failed: {str(e)}")

def generate_clinical_recommendations(predictions, heatmap_data):
    """
    Generate clinical recommendations based on AI analysis results.

    Args:
        predictions: Model predictions dictionary
        heatmap_data: Brain region importance data

    Returns:
        dict: Clinical recommendations and observations
    """
    recommendations = {
        'primary_findings': [],
        'clinical_recommendations': [],
        'follow_up_suggestions': [],
        'technical_notes': []
    }

    # Analyze ADD prediction
    if 'ADD' in predictions:
        add_result = predictions['ADD']
        confidence = add_result['confidence']
        prediction = add_result['prediction']

        if prediction == 1:  # AD detected
            if confidence >= 0.9:
                recommendations['primary_findings'].append(
                    f"High confidence Alzheimer's Disease pattern detected (confidence: {confidence:.1%})"
                )
                recommendations['clinical_recommendations'].append(
                    "Consider comprehensive neuropsychological evaluation"
                )
                recommendations['clinical_recommendations'].append(
                    "Recommend discussion of findings with patient and family"
                )
                recommendations['follow_up_suggestions'].append(
                    "Consider referral to memory disorders clinic"
                )
            elif confidence >= 0.7:
                recommendations['primary_findings'].append(
                    f"Moderate confidence Alzheimer's Disease pattern detected (confidence: {confidence:.1%})"
                )
                recommendations['clinical_recommendations'].append(
                    "Correlate with clinical presentation and cognitive testing"
                )
                recommendations['follow_up_suggestions'].append(
                    "Consider follow-up imaging in 6-12 months"
                )
            else:
                recommendations['primary_findings'].append(
                    f"Possible Alzheimer's Disease pattern (confidence: {confidence:.1%})"
                )
                recommendations['clinical_recommendations'].append(
                    "Clinical correlation strongly recommended"
                )
        else:  # Normal cognition
            if confidence >= 0.8:
                recommendations['primary_findings'].append(
                    f"Normal cognitive pattern detected (confidence: {confidence:.1%})"
                )
                recommendations['clinical_recommendations'].append(
                    "No immediate cognitive concerns identified on imaging"
                )
            else:
                recommendations['primary_findings'].append(
                    f"Indeterminate pattern (confidence: {confidence:.1%})"
                )
                recommendations['clinical_recommendations'].append(
                    "Clinical correlation and possible repeat imaging recommended"
                )

    # Analyze COG score
    if 'COG' in predictions:
        cog_result = predictions['COG']
        score = cog_result['score']
        interpretation = cog_result['interpretation']

        recommendations['primary_findings'].append(
            f"Cognitive assessment score: {score:.3f} ({interpretation})"
        )

        if score < -1.0:
            recommendations['clinical_recommendations'].append(
                "Significant cognitive impairment suggested - comprehensive evaluation recommended"
            )
        elif score < 0:
            recommendations['clinical_recommendations'].append(
                "Mild cognitive changes noted - monitor and correlate clinically"
            )
        else:
            recommendations['clinical_recommendations'].append(
                "Cognitive function within expected range"
            )

    # Analyze heatmap patterns
    if heatmap_data is not None:
        # Calculate heatmap statistics
        max_importance = heatmap_data.max()
        mean_importance = heatmap_data.mean()

        recommendations['technical_notes'].append(
            f"Brain region analysis completed - max importance: {max_importance:.6f}"
        )

        # Identify high-importance regions (simplified analysis)
        high_importance_threshold = max_importance * 0.7
        high_importance_voxels = (heatmap_data > high_importance_threshold).sum()
        total_voxels = heatmap_data.size
        high_importance_percentage = (high_importance_voxels / total_voxels) * 100

        if high_importance_percentage > 5:
            recommendations['technical_notes'].append(
                f"Widespread pattern of importance detected ({high_importance_percentage:.1f}% of brain regions)"
            )
        elif high_importance_percentage > 1:
            recommendations['technical_notes'].append(
                f"Focal pattern of importance detected ({high_importance_percentage:.1f}% of brain regions)"
            )
        else:
            recommendations['technical_notes'].append(
                f"Minimal focal changes detected ({high_importance_percentage:.1f}% of brain regions)"
            )

    # Add standard clinical notes
    recommendations['clinical_recommendations'].append(
        "AI analysis should be interpreted in conjunction with clinical findings"
    )
    recommendations['follow_up_suggestions'].append(
        "Consider multidisciplinary team discussion for complex cases"
    )

    return recommendations

def generate_pdf_report(mri_data, heatmap_data, predictions, filename_base):
    """
    Generate a comprehensive PDF report with MRI analysis results.

    Args:
        mri_data: Preprocessed MRI data
        heatmap_data: SHAP/saliency heatmap data
        predictions: Model predictions
        filename_base: Base filename for the report

    Returns:
        bytes: PDF file as bytes for download
    """
    try:
        import matplotlib.pyplot as plt
        from matplotlib.backends.backend_pdf import PdfPages
        from datetime import datetime
        import io

        # Generate clinical recommendations
        recommendations = generate_clinical_recommendations(predictions, heatmap_data)

        # Create PDF in memory
        pdf_buffer = io.BytesIO()

        with PdfPages(pdf_buffer) as pdf:
            # Page 1: Cover page and summary
            fig, ax = plt.subplots(figsize=(8.5, 11))
            ax.axis('off')

            # Header
            ax.text(0.5, 0.95, 'Demetify', fontsize=32, fontweight='bold',
                   ha='center', va='top', color='#2E86AB')
            ax.text(0.5, 0.90, 'AI-Powered Radiologist Assistant', fontsize=16,
                   ha='center', va='top', color='#A23B72')
            ax.text(0.5, 0.85, 'MRI-Based Dementia Assessment Report', fontsize=14,
                   ha='center', va='top')

            # UIUC branding
            ax.text(0.5, 0.80, 'University of Illinois Urbana-Champaign', fontsize=12,
                   ha='center', va='top', style='italic')
            ax.text(0.5, 0.77, 'Project Lead: S. Seshadri', fontsize=10,
                   ha='center', va='top')

            # Report details
            ax.text(0.1, 0.70, f'Report Generated: {datetime.now().strftime("%B %d, %Y at %I:%M %p")}',
                   fontsize=12, fontweight='bold')
            ax.text(0.1, 0.67, f'Scan ID: {filename_base}', fontsize=12)
            ax.text(0.1, 0.64, f'MRI Shape: {mri_data.shape}', fontsize=12)

            # Results summary
            ax.text(0.1, 0.55, 'ASSESSMENT RESULTS', fontsize=16, fontweight='bold', color='#2E86AB')

            if 'ADD' in predictions:
                add_result = predictions['ADD']
                diagnosis = "Alzheimer's Disease Detected" if add_result['prediction'] == 1 else "Normal Cognition"
                confidence = add_result['confidence']

                ax.text(0.1, 0.50, f'Alzheimer\'s Disease Classification:', fontsize=12, fontweight='bold')
                ax.text(0.15, 0.47, f'• Diagnosis: {diagnosis}', fontsize=11)
                ax.text(0.15, 0.44, f'• Confidence: {confidence:.1%}', fontsize=11)

            if 'COG' in predictions:
                cog_result = predictions['COG']
                score = cog_result['score']
                interpretation = cog_result['interpretation']

                ax.text(0.1, 0.38, f'Cognitive Assessment:', fontsize=12, fontweight='bold')
                ax.text(0.15, 0.35, f'• Score: {score:.3f}', fontsize=11)
                ax.text(0.15, 0.32, f'• Interpretation: {interpretation}', fontsize=11)

            # Clinical Summary (concise, no inference text)
            ax.text(0.1, 0.25, 'CLINICAL SUMMARY', fontsize=16, fontweight='bold', color='#2E86AB')

            y_pos = 0.22
            # Only show key findings, no inference text
            key_findings = []

            if 'ADD' in predictions:
                add_result = predictions['ADD']
                if add_result['prediction'] == 1:
                    key_findings.append(f"Alzheimer's Disease pattern detected (confidence: {add_result['confidence']:.1%})")
                else:
                    key_findings.append(f"Normal cognitive pattern (confidence: {add_result['confidence']:.1%})")

            if 'COG' in predictions:
                cog_result = predictions['COG']
                key_findings.append(f"Cognitive assessment: {cog_result['score']:.3f} ({cog_result['interpretation']})")

            if heatmap_data is not None:
                significant_regions = np.count_nonzero(heatmap_data > heatmap_data.max() * 0.5)
                total_regions = heatmap_data.size
                percentage = (significant_regions / total_regions) * 100
                key_findings.append(f"Brain analysis: {significant_regions:,} significant regions ({percentage:.1f}% of brain)")

            for finding in key_findings:
                ax.text(0.1, y_pos, f'• {finding}', fontsize=11, fontweight='bold')
                y_pos -= 0.03

            # Footer
            ax.text(0.5, 0.05, 'Demetify - Accelerating Radiological Diagnosis',
                   fontsize=10, ha='center', style='italic', color='#666')

            pdf.savefig(fig, bbox_inches='tight')
            plt.close()

            # Page 2: MRI visualizations with brain region analysis
            # Note: heatmap_data is guaranteed to exist since we only generate PDF after heatmap completion
            fig, axes = plt.subplots(2, 3, figsize=(11, 8.5))
            fig.suptitle('MRI Analysis with Brain Region Importance', fontsize=16, fontweight='bold')

            mid_x, mid_y, mid_z = mri_data.shape[0]//2, mri_data.shape[1]//2, mri_data.shape[2]//2

            # Original MRI slices (top row)
            axes[0, 0].imshow(mri_data[:, :, mid_z], cmap='gray')
            axes[0, 0].set_title(f'Original MRI - Axial (Z={mid_z})', fontsize=12)
            axes[0, 0].axis('off')

            axes[0, 1].imshow(mri_data[:, mid_y, :], cmap='gray')
            axes[0, 1].set_title(f'Original MRI - Coronal (Y={mid_y})', fontsize=12)
            axes[0, 1].axis('off')

            axes[0, 2].imshow(mri_data[mid_x, :, :], cmap='gray')
            axes[0, 2].set_title(f'Original MRI - Sagittal (X={mid_x})', fontsize=12)
            axes[0, 2].axis('off')

            # Brain region importance heatmaps (bottom row)
            heatmap_norm = (heatmap_data - heatmap_data.min()) / (heatmap_data.max() - heatmap_data.min())

            im1 = axes[1, 0].imshow(heatmap_norm[:, :, mid_z], cmap='hot', alpha=0.8)
            axes[1, 0].set_title('AI Focus Regions - Axial', fontsize=12, color='#2E86AB')
            axes[1, 0].axis('off')

            axes[1, 1].imshow(heatmap_norm[:, mid_y, :], cmap='hot', alpha=0.8)
            axes[1, 1].set_title('AI Focus Regions - Coronal', fontsize=12, color='#2E86AB')
            axes[1, 1].axis('off')

            axes[1, 2].imshow(heatmap_norm[mid_x, :, :], cmap='hot', alpha=0.8)
            axes[1, 2].set_title('AI Focus Regions - Sagittal', fontsize=12, color='#2E86AB')
            axes[1, 2].axis('off')

            # Add colorbar for heatmaps
            cbar = plt.colorbar(im1, ax=axes[1, :], orientation='horizontal',
                        fraction=0.05, pad=0.1, label='AI Attention Score')
            cbar.ax.tick_params(labelsize=10)

            pdf.savefig(fig, bbox_inches='tight')
            plt.close()

            # Page 3: Prediction Confidence and Analysis Charts
            fig = plt.figure(figsize=(8.5, 11))

            # Create a grid layout similar to website
            gs = fig.add_gridspec(4, 2, height_ratios=[0.8, 1.5, 1.5, 0.8], hspace=0.3, wspace=0.3)

            # Header
            header_ax = fig.add_subplot(gs[0, :])
            header_ax.axis('off')
            header_ax.text(0.5, 0.5, 'AI Analysis Results & Confidence Metrics',
                          fontsize=18, fontweight='bold', ha='center', va='center', color='#2E86AB')

            # ADD Prediction Chart (like website)
            add_ax = fig.add_subplot(gs[1, 0])
            if 'ADD' in predictions:
                add_result = predictions['ADD']
                confidence = add_result['confidence']
                prediction = add_result['prediction']

                # Create confidence bar chart
                categories = ['Normal', 'Alzheimer\'s Disease']
                confidences = [1-confidence if prediction == 1 else confidence,
                              confidence if prediction == 1 else 1-confidence]
                colors = ['#90EE90' if prediction == 0 else '#FFB6C1',
                         '#FF6B6B' if prediction == 1 else '#D3D3D3']

                bars = add_ax.bar(categories, confidences, color=colors, alpha=0.8)
                add_ax.set_ylim(0, 1)
                add_ax.set_ylabel('Confidence Score', fontsize=10)
                add_ax.set_title('Alzheimer\'s Disease Classification', fontsize=12, fontweight='bold', color='#2E86AB')

                # Add confidence text on bars
                for bar, conf in zip(bars, confidences):
                    height = bar.get_height()
                    add_ax.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                               f'{conf:.1%}', ha='center', va='bottom', fontweight='bold')

                # Highlight predicted class
                predicted_idx = prediction
                bars[predicted_idx].set_edgecolor('black')
                bars[predicted_idx].set_linewidth(3)

            # COG Score Chart (like website)
            cog_ax = fig.add_subplot(gs[1, 1])
            if 'COG' in predictions:
                cog_result = predictions['COG']
                score = cog_result['score']

                # Create gauge-like visualization
                theta = np.linspace(0, np.pi, 100)
                r = 1

                # Background semicircle
                cog_ax.plot(r * np.cos(theta), r * np.sin(theta), 'lightgray', linewidth=8)

                # Score indicator
                score_normalized = max(0, min(1, (score + 2) / 4))  # Normalize to 0-1
                score_angle = np.pi * (1 - score_normalized)

                # Color based on score
                if score < -1:
                    color = '#FF6B6B'  # Red for severe
                elif score < 0:
                    color = '#FFA500'  # Orange for mild
                else:
                    color = '#90EE90'  # Green for normal

                cog_ax.plot([0, r * np.cos(score_angle)], [0, r * np.sin(score_angle)],
                           color=color, linewidth=6, marker='o', markersize=8)

                cog_ax.set_xlim(-1.2, 1.2)
                cog_ax.set_ylim(-0.2, 1.2)
                cog_ax.set_aspect('equal')
                cog_ax.axis('off')
                cog_ax.set_title('Cognitive Assessment Score', fontsize=12, fontweight='bold', color='#2E86AB')
                cog_ax.text(0, -0.1, f'Score: {score:.3f}', ha='center', fontsize=11, fontweight='bold')
                cog_ax.text(0, -0.2, f'{cog_result["interpretation"]}', ha='center', fontsize=10, style='italic')

            # Brain Region Importance Statistics
            stats_ax = fig.add_subplot(gs[2, :])
            if heatmap_data is not None:
                # Calculate statistics for visualization
                flat_heatmap = heatmap_data.flatten()

                # Create histogram of importance values
                n_bins = 50
                counts, bins, patches = stats_ax.hist(flat_heatmap, bins=n_bins, alpha=0.7, color='#FF6B6B', edgecolor='black')

                # Color gradient for histogram
                for i, (patch, bin_val) in enumerate(zip(patches, bins[:-1])):
                    normalized_val = (bin_val - flat_heatmap.min()) / (flat_heatmap.max() - flat_heatmap.min())
                    patch.set_facecolor(plt.cm.hot(normalized_val))

                stats_ax.set_xlabel('Brain Region Importance Score', fontsize=10)
                stats_ax.set_ylabel('Number of Voxels', fontsize=10)
                stats_ax.set_title('Distribution of Brain Region Importance', fontsize=12, fontweight='bold', color='#2E86AB')

                # Add statistics text
                stats_text = f'Max: {flat_heatmap.max():.6f} | Mean: {flat_heatmap.mean():.6f} | Std: {flat_heatmap.std():.6f}'
                stats_ax.text(0.5, 0.95, stats_text, transform=stats_ax.transAxes,
                             ha='center', va='top', fontsize=9, bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))

            # Clinical Summary
            summary_ax = fig.add_subplot(gs[3, :])
            summary_ax.axis('off')

            # Create summary text like website
            summary_text = "CLINICAL SUMMARY:\n"
            if 'ADD' in predictions:
                add_result = predictions['ADD']
                diagnosis = "Alzheimer's Disease Pattern Detected" if add_result['prediction'] == 1 else "Normal Cognitive Pattern"
                summary_text += f"• {diagnosis} (Confidence: {add_result['confidence']:.1%})\n"

            if 'COG' in predictions:
                cog_result = predictions['COG']
                summary_text += f"• Cognitive Score: {cog_result['score']:.3f} ({cog_result['interpretation']})\n"

            summary_text += f"• Brain region analysis completed with {np.count_nonzero(heatmap_data):,} significant regions identified"

            summary_ax.text(0.5, 0.5, summary_text, ha='center', va='center', fontsize=11,
                           bbox=dict(boxstyle='round,pad=0.5', facecolor='#E8F4FD', alpha=0.8),
                           transform=summary_ax.transAxes)

            pdf.savefig(fig, bbox_inches='tight')
            plt.close()

        # Return PDF bytes
        pdf_buffer.seek(0)
        pdf_bytes = pdf_buffer.read()
        pdf_buffer.close()

        return pdf_bytes

    except Exception as e:
        st.error(f"❌ Error generating PDF report: {str(e)}")
        import traceback
        st.error(f"Details: {traceback.format_exc()}")
        return None

def display_predictions(predictions):
    """Display prediction results in a medical-friendly format"""

    st.markdown('<h3 class="sub-header">📊 Assessment Results</h3>', unsafe_allow_html=True)
    
    # ADD (Alzheimer's Disease) Results
    if 'ADD' in predictions:
        add_result = predictions['ADD']
        
        st.markdown("### 🧠 Alzheimer's Disease Classification")
        
        # Main prediction
        confidence = add_result['confidence']
        
        # Color coding based on prediction
        if add_result['prediction'] == 1:
            st.markdown(f"""
            <div class="metric-card prediction-high">
                <h4>⚠️ Alzheimer's Disease Detected</h4>
                <p><strong>Confidence:</strong> {confidence:.1%}</p>
            </div>
            """, unsafe_allow_html=True)
        else:
            st.markdown(f"""
            <div class="metric-card prediction-normal">
                <h4>✅ Normal Cognition</h4>
                <p><strong>Confidence:</strong> {confidence:.1%}</p>
            </div>
            """, unsafe_allow_html=True)
        
        # Probability breakdown
        probs = add_result['probabilities']
        
        # Create probability chart
        fig = go.Figure(data=[
            go.Bar(
                x=['Normal', 'Alzheimer\'s Disease'],
                y=[probs['Normal'], probs['AD']],
                marker_color=['#28a745' if probs['Normal'] > probs['AD'] else '#6c757d',
                             '#dc3545' if probs['AD'] > probs['Normal'] else '#6c757d']
            )
        ])
        
        fig.update_layout(
            title="Classification Probabilities",
            yaxis_title="Probability",
            xaxis_title="Diagnosis",
            height=300,
            showlegend=False
        )
        
        st.plotly_chart(fig, use_container_width=True)
    
    # COG (Cognitive) Results
    if 'COG' in predictions:
        cog_result = predictions['COG']
        
        st.markdown("### 🧮 Cognitive Assessment")
        
        score = cog_result['score']
        interpretation = cog_result['interpretation']
        
        # Display cognitive score
        st.markdown(f"""
        <div class="metric-card">
            <h4>Cognitive Score: {score:.2f}</h4>
            <p><strong>Interpretation:</strong> {interpretation}</p>
        </div>
        """, unsafe_allow_html=True)
        
        # Cognitive score visualization
        fig = go.Figure(go.Indicator(
            mode = "gauge+number+delta",
            value = score,
            domain = {'x': [0, 1], 'y': [0, 1]},
            title = {'text': "Cognitive Impairment Score"},
            delta = {'reference': 1.0},
            gauge = {
                'axis': {'range': [None, 3]},
                'bar': {'color': "darkblue"},
                'steps': [
                    {'range': [0, 0.5], 'color': "lightgreen"},
                    {'range': [0.5, 1.5], 'color': "yellow"},
                    {'range': [1.5, 3], 'color': "lightcoral"}
                ],
                'threshold': {
                    'line': {'color': "red", 'width': 4},
                    'thickness': 0.75,
                    'value': 1.5
                }
            }
        ))
        
        fig.update_layout(height=300)
        st.plotly_chart(fig, use_container_width=True)
    
    # Clinical interpretation
    st.markdown("### 📋 Clinical Summary")
    
    if 'ADD' in predictions and 'COG' in predictions:
        add_pred = predictions['ADD']['prediction']
        cog_score = predictions['COG']['score']
        
        if add_pred == 1 and cog_score > 1.5:
            summary = "⚠️ **High Risk**: Both AD classification and cognitive assessment indicate significant impairment."
        elif add_pred == 1 or cog_score > 1.5:
            summary = "⚠️ **Moderate Risk**: One assessment indicates potential impairment. Further evaluation recommended."
        elif cog_score > 0.5:
            summary = "⚡ **Mild Concern**: Mild cognitive changes detected. Monitoring recommended."
        else:
            summary = "✅ **Normal**: Both assessments indicate normal cognitive function."
        
        st.markdown(f"""
        <div class="metric-card">
            {summary}
        </div>
        """, unsafe_allow_html=True)
    
    # Additional analysis options
    st.markdown("---")

if __name__ == "__main__":
    main()
