#!/usr/bin/env python3
"""
Simple test to verify heatmap overlay works in Streamlit
"""

import streamlit as st
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.colors import LinearSegmentedColormap

def create_test_overlay():
    """Create a simple test overlay to verify it works"""
    
    # Create fake MRI data
    mri_data = np.random.rand(91, 109, 91) * 0.8 + 0.2
    
    # Create fake heatmap with clear activation
    heatmap = np.zeros_like(mri_data)
    
    # Add strong activation in center
    center_x, center_y, center_z = 45, 54, 45
    x, y, z = np.meshgrid(
        np.arange(mri_data.shape[0]) - center_x,
        np.arange(mri_data.shape[1]) - center_y, 
        np.arange(mri_data.shape[2]) - center_z,
        indexing='ij'
    )
    
    # Create circular activation
    activation_mask = (x**2 + y**2 + z**2) < 400
    heatmap[activation_mask] = 1.0
    
    # Add some smaller activations
    activation_mask2 = ((x-10)**2 + (y+10)**2 + z**2) < 100
    heatmap[activation_mask2] = 0.7
    
    activation_mask3 = ((x+15)**2 + (y-5)**2 + z**2) < 150
    heatmap[activation_mask3] = 0.5
    
    return mri_data, heatmap

def display_test_overlay(mri_data, heatmap):
    """Display test overlay"""
    
    # Get middle slices
    mid_x, mid_y, mid_z = mri_data.shape[0]//2, mri_data.shape[1]//2, mri_data.shape[2]//2
    
    # Create figure
    fig, axes = plt.subplots(1, 3, figsize=(18, 6))
    fig.suptitle('🔥 SIMPLE HEATMAP TEST - Should Show RED/YELLOW', fontsize=16, fontweight='bold')
    
    # Normalize data
    mri_norm = (mri_data - mri_data.min()) / (mri_data.max() - mri_data.min())
    heatmap_norm = heatmap / heatmap.max() if heatmap.max() > 0 else heatmap
    
    # Create red-yellow colormap
    colors = [(0, 0, 0, 0), (1, 0, 0, 0.8), (1, 1, 0, 1.0)]  # Transparent -> Red -> Yellow
    heatmap_cmap = LinearSegmentedColormap.from_list('test_heatmap', colors, N=256)
    
    # AXIAL VIEW
    axial_mri = np.rot90(mri_norm[:, :, mid_z])
    axial_heatmap = np.rot90(heatmap_norm[:, :, mid_z])
    
    axes[0].imshow(axial_mri, cmap='gray', aspect='equal', vmin=0, vmax=1)
    axial_heatmap_masked = np.ma.masked_where(axial_heatmap < 0.01, axial_heatmap)
    im1 = axes[0].imshow(axial_heatmap_masked, cmap=heatmap_cmap, aspect='equal', vmin=0, vmax=1)
    axes[0].set_title(f'Axial - Max: {axial_heatmap.max():.3f}', fontweight='bold')
    axes[0].axis('off')
    
    # CORONAL VIEW
    coronal_mri = np.rot90(mri_norm[:, mid_y, :])
    coronal_heatmap = np.rot90(heatmap_norm[:, mid_y, :])
    
    axes[1].imshow(coronal_mri, cmap='gray', aspect='equal', vmin=0, vmax=1)
    coronal_heatmap_masked = np.ma.masked_where(coronal_heatmap < 0.01, coronal_heatmap)
    im2 = axes[1].imshow(coronal_heatmap_masked, cmap=heatmap_cmap, aspect='equal', vmin=0, vmax=1)
    axes[1].set_title(f'Coronal - Max: {coronal_heatmap.max():.3f}', fontweight='bold')
    axes[1].axis('off')
    
    # SAGITTAL VIEW
    sagittal_mri = np.rot90(mri_norm[mid_x, :, :])
    sagittal_heatmap = np.rot90(heatmap_norm[mid_x, :, :])
    
    axes[2].imshow(sagittal_mri, cmap='gray', aspect='equal', vmin=0, vmax=1)
    sagittal_heatmap_masked = np.ma.masked_where(sagittal_heatmap < 0.01, sagittal_heatmap)
    im3 = axes[2].imshow(sagittal_heatmap_masked, cmap=heatmap_cmap, aspect='equal', vmin=0, vmax=1)
    axes[2].set_title(f'Sagittal - Max: {sagittal_heatmap.max():.3f}', fontweight='bold')
    axes[2].axis('off')
    
    # Add colorbar
    cbar = plt.colorbar(im3, ax=axes, orientation='horizontal', fraction=0.05, pad=0.1, shrink=0.8)
    cbar.set_label('Heatmap Intensity (Should be RED/YELLOW)', fontsize=12, fontweight='bold')
    
    plt.tight_layout()
    return fig

def main():
    st.title("🔥 Simple Heatmap Overlay Test")
    st.markdown("**This tests if red/yellow heatmap overlays work in Streamlit**")
    
    if st.button("🧠 Generate Test Heatmap Overlay"):
        with st.spinner("Creating test overlay..."):
            # Create test data
            mri_data, heatmap = create_test_overlay()
            
            # Calculate statistics
            total_voxels = np.prod(heatmap.shape)
            active_voxels = np.sum(heatmap > 0.01)
            activation_percentage = (active_voxels / total_voxels) * 100
            
            st.success(f"✅ Test data created!")
            
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Heatmap Max", f"{heatmap.max():.3f}")
            with col2:
                st.metric("Activation %", f"{activation_percentage:.2f}%")
            with col3:
                st.metric("Active voxels", f"{np.sum(heatmap > 0):,}")
            
            # Create and display overlay
            fig = display_test_overlay(mri_data, heatmap)
            st.pyplot(fig, use_container_width=True, clear_figure=True)
            plt.close(fig)
            
            if activation_percentage > 5.0:
                st.success("🎉 **HIGH ACTIVATION** - You should see clear red/yellow regions!")
            else:
                st.warning("⚠️ **LOW ACTIVATION** - Heatmap may be faint")
            
            st.markdown("""
            ### 📋 What You Should See:
            
            1. **Gray brain scans** as background
            2. **Red circular regions** overlaid on the scans
            3. **Yellow areas** in the center of red regions (highest intensity)
            4. **Colorbar** showing the intensity scale
            
            If you don't see red/yellow regions, there's an issue with the overlay rendering.
            """)

if __name__ == "__main__":
    main()
