@echo off
REM Demetify NCOMMS2022 Windows Deployment Script

echo 🧠 Demetify - NCOMMS2022 Frontend Deployment
echo =============================================

REM Check if conda is available
where conda >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Conda not found. Please install Miniconda/Anaconda first.
    pause
    exit /b 1
)

REM Activate environment
echo 📦 Activating conda environment 'abstract'...
call conda activate abstract

REM Install requirements
echo 📦 Installing Python requirements...
pip install -r requirements_frontend.txt

REM Check if models exist
if not exist "ncomms2022_original\checkpoint_dir" (
    echo ❌ Model checkpoints not found. Please ensure ncomms2022_original is properly set up.
    pause
    exit /b 1
)

echo ✅ Setup complete!
echo.
echo 🚀 Starting Demetify Frontend...
echo    Access the application at: http://localhost:8501
echo    Press Ctrl+C to stop the server
echo.

REM Start Streamlit
streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address 0.0.0.0

pause
