#!/usr/bin/env python3
"""
Test Gradient Fix - Verify the gradient warnings are resolved
"""

import numpy as np
import torch
from final_mci_streamlit_app import FinalMCIInferenceEngine
import logging

# Set up logging to capture warnings
logging.basicConfig(level=logging.INFO)

def test_gradient_fix():
    """Test that gradient warnings are resolved"""
    
    print("🔧 TESTING GRADIENT FIX")
    print("=" * 50)
    
    # Create inference engine
    engine = FinalMCIInferenceEngine()
    
    print(f"✅ Models loaded:")
    print(f"   CNN Model: {'✅' if engine.cnn_model is not None else '❌'}")
    print(f"   Gated Model: {'✅' if engine.gated_model is not None else '❌'}")
    print(f"   Gradient-Optimized Model: {'✅' if engine.gradient_optimized_model is not None else '❌'}")
    
    # Create test MRI data
    test_cases = [
        {
            'name': 'Healthy Brain',
            'mri': create_test_mri('healthy')
        },
        {
            'name': 'MCI Brain',
            'mri': create_test_mri('mci')
        },
        {
            'name': 'AD Brain',
            'mri': create_test_mri('ad')
        }
    ]
    
    for case in test_cases:
        print(f"\n🧪 Testing: {case['name']}")
        
        try:
            # Run comprehensive prediction
            results = engine.comprehensive_predict(case['mri'])
            
            print(f"   ✅ Prediction successful")
            print(f"   📊 Models tested: {len(results['models'])}")
            
            # Check each model's results
            for model_name, model_results in results['models'].items():
                print(f"   🔍 {model_name}:")
                print(f"      MMSE Score: {model_results['cognitive_score']:.1f}")
                print(f"      Predicted Class: {['CN', 'MCI', 'AD'][model_results['predicted_class']]}")
                print(f"      Heatmap Shape: {model_results['heatmap'].shape}")
                print(f"      Heatmap Range: {model_results['heatmap'].min():.4f} - {model_results['heatmap'].max():.4f}")
                
                # Check if this is the gradient-optimized model
                if 'gradient_quality' in model_results:
                    print(f"      🔥 Gradient Quality: {model_results['gradient_quality']}")
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
    
    print(f"\n🎯 GRADIENT FIX TEST COMPLETE!")

def create_test_mri(brain_type):
    """Create test MRI data"""
    
    if brain_type == 'healthy':
        mri = np.random.normal(0.7, 0.1, (91, 109, 91))
        # Add healthy tissue structure
        center = (45, 54, 45)
        for i in range(center[0]-15, center[0]+15):
            for j in range(center[1]-20, center[1]+20):
                for k in range(center[2]-15, center[2]+15):
                    if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                        mri[i, j, k] += np.random.normal(0.15, 0.03)
                        
    elif brain_type == 'mci':
        mri = np.random.normal(0.5, 0.12, (91, 109, 91))
        # Add mild atrophy
        center = (45, 54, 45)
        for i in range(center[0]-10, center[0]+10):
            for j in range(center[1]-15, center[1]+15):
                for k in range(center[2]-10, center[2]+10):
                    if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                        mri[i, j, k] *= np.random.uniform(0.85, 0.95)
                        
    else:  # AD
        mri = np.random.normal(0.3, 0.15, (91, 109, 91))
        # Add severe atrophy
        center = (45, 54, 45)
        for i in range(center[0]-15, center[0]+15):
            for j in range(center[1]-20, center[1]+20):
                for k in range(center[2]-15, center[2]+15):
                    if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                        mri[i, j, k] *= np.random.uniform(0.4, 0.7)
    
    return np.clip(mri, 0, 1)

def test_direct_gradient_computation():
    """Test direct gradient computation with gradient-optimized model"""
    
    print("\n🔥 TESTING DIRECT GRADIENT COMPUTATION")
    print("=" * 50)
    
    engine = FinalMCIInferenceEngine()
    
    if engine.gradient_optimized_model is None:
        print("❌ No gradient-optimized model available")
        return
    
    # Create test input
    test_mri = create_test_mri('mci')
    mri_tensor = torch.FloatTensor(test_mri).unsqueeze(0).unsqueeze(0)
    mri_tensor.requires_grad_(True)
    
    print(f"📊 Input tensor shape: {mri_tensor.shape}")
    print(f"📊 Input requires_grad: {mri_tensor.requires_grad}")
    
    # Forward pass
    outputs = engine.gradient_optimized_model(mri_tensor)
    loss = outputs['cognitive_score'].mean()
    
    print(f"📊 Output cognitive score: {loss.item():.4f}")
    
    # Backward pass
    loss.backward()
    
    if mri_tensor.grad is not None:
        grad_magnitude = torch.abs(mri_tensor.grad).cpu().numpy()
        
        print(f"✅ Gradients computed successfully!")
        print(f"📊 Gradient range: {grad_magnitude.min():.10f} to {grad_magnitude.max():.10f}")
        print(f"📊 Gradient mean: {grad_magnitude.mean():.10f}")
        print(f"📊 Non-zero gradients: {np.count_nonzero(grad_magnitude)}/{grad_magnitude.size}")
        
        if grad_magnitude.max() > 1e-8:
            print("🎉 EXCELLENT: Strong gradients - no more warnings!")
        elif grad_magnitude.max() > 1e-10:
            print("✅ GOOD: Adequate gradients - warnings should be reduced")
        else:
            print("⚠️ WEAK: Gradients still too small")
    else:
        print("❌ No gradients computed")

def test_heatmap_generation():
    """Test heatmap generation specifically"""
    
    print("\n🗺️ TESTING HEATMAP GENERATION")
    print("=" * 40)
    
    engine = FinalMCIInferenceEngine()
    
    # Create test MRI
    test_mri = create_test_mri('mci')
    mri_tensor = torch.FloatTensor(test_mri).unsqueeze(0).unsqueeze(0)
    
    # Test heatmap generation
    try:
        heatmap = engine.generate_real_gradient_heatmap(
            None,  # Will use gradient-optimized model
            mri_tensor, 
            cognitive_score=22.0,
            original_shape=test_mri.shape
        )
        
        print(f"✅ Heatmap generated successfully!")
        print(f"📊 Heatmap shape: {heatmap.shape}")
        print(f"📊 Heatmap range: {heatmap.min():.6f} to {heatmap.max():.6f}")
        print(f"📊 Heatmap mean: {heatmap.mean():.6f}")
        print(f"📊 Non-zero values: {np.count_nonzero(heatmap)}/{heatmap.size}")
        
        if heatmap.max() > 0.1:
            print("🎉 EXCELLENT: Strong heatmap signal!")
        elif heatmap.max() > 0.01:
            print("✅ GOOD: Adequate heatmap signal")
        else:
            print("⚠️ WEAK: Heatmap signal too weak")
            
    except Exception as e:
        print(f"❌ Heatmap generation failed: {e}")

def main():
    """Main test function"""
    
    print("🧠 COMPREHENSIVE GRADIENT FIX TEST")
    print("=" * 70)
    print("Testing the resolution of gradient computation warnings")
    print()
    
    # Test 1: Basic gradient fix
    test_gradient_fix()
    
    # Test 2: Direct gradient computation
    test_direct_gradient_computation()
    
    # Test 3: Heatmap generation
    test_heatmap_generation()
    
    print(f"\n🎉 ALL TESTS COMPLETE!")
    print(f"If you see 'EXCELLENT' or 'GOOD' messages above,")
    print(f"the gradient warnings should be resolved! 🎯")

if __name__ == "__main__":
    main()
