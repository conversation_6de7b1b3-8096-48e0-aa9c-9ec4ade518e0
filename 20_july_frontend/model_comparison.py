#!/usr/bin/env python3
"""
Model Comparison: Original CNN vs Gated CNN
Generate confusion matrices and performance comparison
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.utils.data import DataLoader
import numpy as np
import json
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import confusion_matrix, classification_report
import pandas as pd
from pathlib import Path

from data_loader import get_balanced_datasets

# Original Model Architecture
class RealMCIModel(nn.Module):
    """Original Real MCI model architecture"""
    
    def __init__(self, num_classes=3):
        super(RealMCIModel, self).__init__()
        
        # Efficient 3D CNN for real data
        self.features = nn.Sequential(
            # Block 1
            nn.Conv3d(1, 32, kernel_size=3, padding=1),
            nn.BatchNorm3d(32),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            nn.Dropout3d(0.1),
            
            # Block 2
            nn.Conv3d(32, 64, kernel_size=3, padding=1),
            nn.BatchNorm3d(64),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            nn.Dropout3d(0.1),
            
            # Block 3
            nn.Conv3d(64, 128, kernel_size=3, padding=1),
            nn.BatchNorm3d(128),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            nn.Dropout3d(0.2),
            
            # Global pooling
            nn.AdaptiveAvgPool3d((4, 4, 4))
        )
        
        # Feature size
        self.feature_size = 128 * 4 * 4 * 4
        
        # Multi-task heads
        self.classifier = nn.Sequential(
            nn.Linear(self.feature_size, 512),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )
        
        self.atrophy_head = nn.Sequential(
            nn.Linear(self.feature_size, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 1),
            nn.Sigmoid()
        )
        
        self.clinical_head = nn.Sequential(
            nn.Linear(self.feature_size, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 3)
        )
    
    def forward(self, x):
        features = self.features(x)
        features = features.view(features.size(0), -1)
        
        classification = self.classifier(features)
        atrophy = self.atrophy_head(features)
        clinical = self.clinical_head(features)
        
        return {
            'classification': classification,
            'atrophy': atrophy,
            'clinical': clinical,
            'features': features
        }

# Gated CNN Architecture (from full training)
class GatedConv3D(nn.Module):
    def __init__(self, in_channels, out_channels, kernel_size=3, padding=1):
        super(GatedConv3D, self).__init__()
        self.conv = nn.Conv3d(in_channels, out_channels, kernel_size, padding=padding)
        self.gate_conv = nn.Conv3d(in_channels, out_channels, kernel_size, padding=padding)
        self.bn = nn.BatchNorm3d(out_channels)
        
    def forward(self, x):
        main = self.conv(x)
        gate = torch.sigmoid(self.gate_conv(x))
        output = main * gate
        return self.bn(output)

class FullGatedCNNModel(nn.Module):
    def __init__(self, num_classes=3):
        super(FullGatedCNNModel, self).__init__()
        
        self.features = nn.Sequential(
            GatedConv3D(1, 32),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            nn.Dropout3d(0.1),
            
            GatedConv3D(32, 64),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            nn.Dropout3d(0.1),
            
            GatedConv3D(64, 128),
            nn.ReLU(inplace=True),
            nn.MaxPool3d(2),
            nn.Dropout3d(0.2),
            
            nn.AdaptiveAvgPool3d((4, 4, 4))
        )
        
        self.feature_size = 128 * 4 * 4 * 4
        
        self.classifier = nn.Sequential(
            nn.Linear(self.feature_size, 512),
            nn.ReLU(),
            nn.Dropout(0.5),
            nn.Linear(512, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, num_classes)
        )
        
        self.atrophy_head = nn.Sequential(
            nn.Linear(self.feature_size, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 1),
            nn.Sigmoid()
        )
        
        self.clinical_head = nn.Sequential(
            nn.Linear(self.feature_size, 256),
            nn.ReLU(),
            nn.Dropout(0.3),
            nn.Linear(256, 128),
            nn.ReLU(),
            nn.Dropout(0.2),
            nn.Linear(128, 3)
        )
    
    def forward(self, x):
        features = self.features(x)
        features = features.view(features.size(0), -1)
        
        classification = self.classifier(features)
        atrophy = self.atrophy_head(features)
        clinical = self.clinical_head(features)
        
        return {
            'classification': classification,
            'atrophy': atrophy,
            'clinical': clinical,
            'features': features
        }

def evaluate_model(model, test_loader, device, model_name):
    """Evaluate model and generate confusion matrix"""
    
    model.eval()
    test_predictions = []
    test_labels = []
    test_correct = 0
    test_total = 0
    
    with torch.no_grad():
        for batch in test_loader:
            mri_data = batch['mri'].to(device)
            labels = batch['label'].to(device)
            
            outputs = model(mri_data)
            _, predicted = torch.max(outputs['classification'].data, 1)
            
            test_total += labels.size(0)
            test_correct += (predicted == labels).sum().item()
            
            test_predictions.extend(predicted.cpu().numpy())
            test_labels.extend(labels.cpu().numpy())
    
    test_accuracy = test_correct / test_total
    
    # Generate confusion matrix
    cm = confusion_matrix(test_labels, test_predictions)
    class_names = ['CN', 'MCI', 'AD']
    
    # Plot confusion matrix
    plt.figure(figsize=(10, 8))
    sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                xticklabels=class_names, yticklabels=class_names)
    plt.title(f'{model_name} Confusion Matrix\nAccuracy: {test_accuracy:.1%}')
    plt.ylabel('True Label')
    plt.xlabel('Predicted Label')
    plt.savefig(f'results/{model_name.lower().replace(" ", "_")}_confusion_matrix.png', 
                dpi=300, bbox_inches='tight')
    plt.close()
    
    # Classification report
    class_report = classification_report(test_labels, test_predictions, 
                                       target_names=class_names, output_dict=True)
    
    return {
        'accuracy': test_accuracy,
        'confusion_matrix': cm.tolist(),
        'classification_report': class_report,
        'predictions': test_predictions,
        'true_labels': test_labels
    }

def compare_models():
    """Compare Original CNN vs Gated CNN performance"""
    
    print("🔍 Starting Model Comparison...")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load test dataset
    _, _, test_dataset = get_balanced_datasets()
    test_loader = DataLoader(test_dataset, batch_size=8, shuffle=False, num_workers=4)
    
    results = {}
    
    # Evaluate Original CNN
    if Path('checkpoints/best_real_mci_model.pth').exists():
        print("📊 Evaluating Original CNN...")
        original_model = RealMCIModel().to(device)
        checkpoint = torch.load('checkpoints/best_real_mci_model.pth', map_location=device)
        original_model.load_state_dict(checkpoint['model_state_dict'])
        
        results['Original CNN'] = evaluate_model(original_model, test_loader, device, 'Original CNN')
        print(f"✅ Original CNN Accuracy: {results['Original CNN']['accuracy']:.1%}")
    
    # Evaluate Gated CNN
    if Path('checkpoints/best_full_gated_cnn_model.pth').exists():
        print("📊 Evaluating Gated CNN...")
        gated_model = FullGatedCNNModel().to(device)
        checkpoint = torch.load('checkpoints/best_full_gated_cnn_model.pth', map_location=device)
        gated_model.load_state_dict(checkpoint['model_state_dict'])
        
        results['Gated CNN'] = evaluate_model(gated_model, test_loader, device, 'Gated CNN')
        print(f"✅ Gated CNN Accuracy: {results['Gated CNN']['accuracy']:.1%}")
    
    # Create comparison plot
    if len(results) == 2:
        fig, axes = plt.subplots(1, 2, figsize=(20, 8))
        
        class_names = ['CN', 'MCI', 'AD']
        
        for idx, (model_name, result) in enumerate(results.items()):
            cm = np.array(result['confusion_matrix'])
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                       xticklabels=class_names, yticklabels=class_names,
                       ax=axes[idx])
            axes[idx].set_title(f'{model_name}\nAccuracy: {result["accuracy"]:.1%}')
            axes[idx].set_ylabel('True Label')
            axes[idx].set_xlabel('Predicted Label')
        
        plt.tight_layout()
        plt.savefig('results/model_comparison_confusion_matrices.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("📈 Comparison plot saved: results/model_comparison_confusion_matrices.png")
    
    # Save comparison results
    with open('results/model_comparison_results.json', 'w') as f:
        json.dump(results, f, indent=2)
    
    # Print comparison summary
    print("\n" + "="*60)
    print("📊 MODEL COMPARISON SUMMARY")
    print("="*60)
    
    for model_name, result in results.items():
        print(f"\n🔹 {model_name}:")
        print(f"   Accuracy: {result['accuracy']:.1%}")
        
        # Per-class metrics
        for class_name in ['CN', 'MCI', 'AD']:
            if class_name in result['classification_report']:
                metrics = result['classification_report'][class_name]
                print(f"   {class_name}: Precision={metrics['precision']:.3f}, "
                      f"Recall={metrics['recall']:.3f}, F1={metrics['f1-score']:.3f}")
    
    print("\n🎉 Model comparison completed!")
    return results

if __name__ == "__main__":
    Path('results').mkdir(exist_ok=True)
    results = compare_models()
