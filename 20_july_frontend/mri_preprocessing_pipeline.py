#!/usr/bin/env python3
"""
MRI Preprocessing Pipeline for Neurologist-Ready Frontend
Handles .npy and .nii files, preserves original for visualization, preprocesses for model inference
"""

import numpy as np
import nibabel as nib
from scipy import ndimage
from skimage import transform
import tempfile
import os
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class MRIPreprocessingPipeline:
    """
    Complete MRI preprocessing pipeline that:
    1. Loads .npy or .nii files
    2. Preserves original data for nilearn visualization
    3. Preprocesses data for model inference
    4. Maintains proper orientations and aspect ratios
    """
    
    def __init__(self):
        self.target_shape = (91, 109, 91)  # Standard MRI model input shape
        self.target_spacing = (2.0, 2.0, 2.0)  # Target voxel spacing in mm
        logger.info("🧠 MRI Preprocessing Pipeline initialized")
        
    def load_mri_file(self, file_path_or_bytes, file_type='npy'):
        """
        Load MRI file from path or uploaded bytes
        
        Args:
            file_path_or_bytes: File path string or uploaded file bytes
            file_type: 'nii' or 'npy'
            
        Returns:
            dict: {
                'original_data': numpy array for visualization,
                'original_affine': affine matrix (for .nii files),
                'file_info': metadata about the file
            }
        """
        logger.info(f"📂 Loading MRI file (type: {file_type})")
        
        try:
            if file_type == 'npy':
                return self._load_npy_file(file_path_or_bytes)
            elif file_type in ['nii', 'nii.gz']:
                return self._load_nii_file(file_path_or_bytes)
            else:
                raise ValueError(f"Unsupported file type: {file_type}")
                
        except Exception as e:
            logger.error(f"❌ Failed to load MRI file: {e}")
            raise
    
    def _load_npy_file(self, file_path_or_bytes):
        """Load .npy file"""
        if isinstance(file_path_or_bytes, str):
            data = np.load(file_path_or_bytes)
            filename = Path(file_path_or_bytes).name
        else:
            # Handle uploaded bytes
            with tempfile.NamedTemporaryFile(suffix='.npy', delete=False) as tmp_file:
                tmp_file.write(file_path_or_bytes.read())
                tmp_file.flush()
                data = np.load(tmp_file.name)
                os.unlink(tmp_file.name)
                filename = "uploaded_scan.npy"
        
        logger.info(f"✅ Loaded .npy file: {data.shape}")
        
        return {
            'original_data': data,
            'original_affine': np.eye(4),  # Identity matrix for .npy files
            'file_info': {
                'filename': filename,
                'original_shape': data.shape,
                'data_type': data.dtype,
                'file_type': 'npy'
            }
        }
    
    def _load_nii_file(self, file_path_or_bytes):
        """Load .nii/.nii.gz file"""
        if isinstance(file_path_or_bytes, str):
            nii_img = nib.load(file_path_or_bytes)
            filename = Path(file_path_or_bytes).name
        else:
            # Handle uploaded bytes
            with tempfile.NamedTemporaryFile(suffix='.nii.gz', delete=False) as tmp_file:
                tmp_file.write(file_path_or_bytes.read())
                tmp_file.flush()
                nii_img = nib.load(tmp_file.name)
                os.unlink(tmp_file.name)
                filename = "uploaded_scan.nii.gz"
        
        data = nii_img.get_fdata()
        affine = nii_img.affine
        
        logger.info(f"✅ Loaded .nii file: {data.shape}")
        
        return {
            'original_data': data,
            'original_affine': affine,
            'file_info': {
                'filename': filename,
                'original_shape': data.shape,
                'data_type': data.dtype,
                'file_type': 'nii',
                'voxel_sizes': nib.affines.voxel_sizes(affine)
            }
        }
    
    def preprocess_for_model(self, mri_data):
        """
        Preprocess MRI data for model inference
        
        Args:
            mri_data: Dictionary from load_mri_file()
            
        Returns:
            dict: {
                'preprocessed_tensor': torch tensor ready for model,
                'preprocessing_info': metadata about preprocessing steps
            }
        """
        logger.info("⚙️ Preprocessing MRI for model inference")
        
        original_data = mri_data['original_data']
        
        try:
            # Step 1: Normalize intensity values
            normalized_data = self._normalize_intensity(original_data)
            
            # Step 2: Resize to target shape
            resized_data = self._resize_to_target_shape(normalized_data)
            
            # Step 3: Convert to tensor format
            import torch
            tensor_data = torch.FloatTensor(resized_data).unsqueeze(0).unsqueeze(0)  # Add batch and channel dims
            
            preprocessing_info = {
                'original_shape': original_data.shape,
                'target_shape': self.target_shape,
                'normalization': 'z-score',
                'resize_method': 'zoom',
                'tensor_shape': tensor_data.shape
            }
            
            logger.info(f"✅ Preprocessing complete: {original_data.shape} → {tensor_data.shape}")
            
            return {
                'preprocessed_tensor': tensor_data,
                'preprocessing_info': preprocessing_info
            }
            
        except Exception as e:
            logger.error(f"❌ Preprocessing failed: {e}")
            raise
    
    def _normalize_intensity(self, data):
        """Normalize MRI intensity values using z-score normalization"""
        # Remove background (values close to 0)
        mask = data > np.percentile(data, 1)
        
        if np.sum(mask) > 0:
            mean_val = np.mean(data[mask])
            std_val = np.std(data[mask])
            
            if std_val > 0:
                normalized = (data - mean_val) / std_val
            else:
                normalized = data - mean_val
        else:
            normalized = data
        
        # Clip extreme values
        normalized = np.clip(normalized, -5, 5)
        
        return normalized
    
    def _resize_to_target_shape(self, data):
        """Resize data to target shape using zoom"""
        current_shape = data.shape
        zoom_factors = [target_dim / current_dim for target_dim, current_dim in zip(self.target_shape, current_shape)]
        
        resized_data = ndimage.zoom(data, zoom_factors, order=1, mode='constant', cval=0)
        
        # Ensure exact target shape
        if resized_data.shape != self.target_shape:
            # Pad or crop to exact shape
            resized_data = self._pad_or_crop_to_shape(resized_data, self.target_shape)
        
        return resized_data
    
    def _pad_or_crop_to_shape(self, data, target_shape):
        """Pad or crop data to exact target shape"""
        current_shape = data.shape
        
        # Calculate padding/cropping for each dimension
        pad_crop = []
        for i in range(len(target_shape)):
            diff = target_shape[i] - current_shape[i]
            if diff > 0:
                # Need padding
                pad_before = diff // 2
                pad_after = diff - pad_before
                pad_crop.append((pad_before, pad_after))
            elif diff < 0:
                # Need cropping
                crop_before = (-diff) // 2
                crop_after = crop_before + (-diff) % 2
                pad_crop.append((crop_before, -crop_after if crop_after > 0 else None))
            else:
                pad_crop.append((0, 0))
        
        # Apply padding
        if any(p[0] > 0 or p[1] > 0 for p in pad_crop if isinstance(p[1], int)):
            data = np.pad(data, [(max(0, p[0]), max(0, p[1])) for p in pad_crop], mode='constant', constant_values=0)
        
        # Apply cropping
        slices = []
        for i, (crop_before, crop_after) in enumerate(pad_crop):
            if crop_before > 0:
                if crop_after is None:
                    slices.append(slice(crop_before, None))
                else:
                    slices.append(slice(crop_before, -crop_after if crop_after > 0 else None))
            else:
                slices.append(slice(None))
        
        if any(s != slice(None) for s in slices):
            data = data[tuple(slices)]
        
        return data
    
    def create_nilearn_compatible_data(self, mri_data):
        """
        Create data compatible with nilearn for visualization
        
        Args:
            mri_data: Dictionary from load_mri_file()
            
        Returns:
            dict: Data ready for nilearn visualization
        """
        logger.info("🎨 Preparing data for nilearn visualization")
        
        original_data = mri_data['original_data']
        original_affine = mri_data['original_affine']
        
        # Ensure proper orientation for radiologist viewing
        # (This maintains the original orientation without flipping)
        
        return {
            'data': original_data,
            'affine': original_affine,
            'ready_for_nilearn': True
        }

# Example usage and testing
if __name__ == "__main__":
    # Test the pipeline
    pipeline = MRIPreprocessingPipeline()
    
    # Test with a sample file from our collection
    test_file = "experiment_25_scans/CASE_01_mri.npy"
    if Path(test_file).exists():
        print("🧪 Testing MRI Preprocessing Pipeline")
        
        # Load file
        mri_data = pipeline.load_mri_file(test_file, 'npy')
        print(f"✅ Loaded: {mri_data['file_info']}")
        
        # Preprocess for model
        preprocessed = pipeline.preprocess_for_model(mri_data)
        print(f"✅ Preprocessed: {preprocessed['preprocessing_info']}")
        
        # Prepare for visualization
        viz_data = pipeline.create_nilearn_compatible_data(mri_data)
        print(f"✅ Visualization ready: {viz_data['data'].shape}")
        
        print("🎉 Pipeline test successful!")
    else:
        print("❌ Test file not found")
