#!/bin/bash
# Illinois Cluster Setup Script for Enhanced MCI Model Training
# Author: Enhanced MRI Frontend Team
# Date: $(date)

echo "🧠 Illinois Cluster Setup for Enhanced MCI Model Training"
echo "=========================================================="
echo ""

# Cluster connection info
CLUSTER_LOGIN="<EMAIL>"
PROJECT_DIR="/projects/illinois/cob/ba/sridhar"
ACCOUNT="sridhar-ic"

echo "📋 Cluster Information:"
echo "  Login: $CLUSTER_LOGIN"
echo "  Project Directory: $PROJECT_DIR"
echo "  Account: $ACCOUNT"
echo ""

# Function to check if we're on the cluster
check_cluster_environment() {
    if [[ $(hostname) == *"campuscluster"* ]]; then
        echo "✅ Running on Illinois Campus Cluster"
        return 0
    else
        echo "❌ Not on Illinois Campus Cluster"
        echo "Please run: ssh -l vmt2 cc-login1.campuscluster.illinois.edu"
        return 1
    fi
}

# Function to navigate to project directory
setup_project_directory() {
    echo "📁 Setting up project directory..."
    
    if [ ! -d "$PROJECT_DIR" ]; then
        echo "❌ Project directory not found: $PROJECT_DIR"
        return 1
    fi
    
    cd "$PROJECT_DIR" || return 1
    echo "✅ Changed to project directory: $(pwd)"
    
    # Create enhanced MCI working directory
    WORK_DIR="$PROJECT_DIR/enhanced_mci_training"
    if [ ! -d "$WORK_DIR" ]; then
        mkdir -p "$WORK_DIR"
        echo "✅ Created working directory: $WORK_DIR"
    fi
    
    cd "$WORK_DIR" || return 1
    echo "✅ Working directory: $(pwd)"
}

# Function to explore data structure
explore_data_structure() {
    echo "🔍 Exploring NACC data structure..."
    
    cd "$PROJECT_DIR" || return 1
    
    echo ""
    echo "📊 Available data directories:"
    ls -la | grep -E "(nacc|NACC|preprocessed)" | head -10
    
    echo ""
    echo "📋 NACC data dump contents:"
    if [ -d "nacc_data_dump" ]; then
        ls -la nacc_data_dump/ | head -10
        echo "  Total files: $(find nacc_data_dump -type f | wc -l)"
    else
        echo "  ❌ nacc_data_dump directory not found"
    fi
    
    echo ""
    echo "📋 NACC tabular data contents:"
    if [ -d "NACC_tabular_data" ]; then
        ls -la NACC_tabular_data/ | head -10
        echo "  Total files: $(find NACC_tabular_data -type f | wc -l)"
    else
        echo "  ❌ NACC_tabular_data directory not found"
    fi
    
    echo ""
    echo "📋 NACC imaging files:"
    if [ -d "Nacc imaging file" ]; then
        ls -la "Nacc imaging file"/ | head -10
        echo "  Total files: $(find "Nacc imaging file" -type f | wc -l)"
    else
        echo "  ❌ Nacc imaging file directory not found"
    fi
    
    echo ""
    echo "📋 Preprocessed T1 scans:"
    if [ -d "preprocessedT1scans23june" ]; then
        ls -la preprocessedT1scans23june/ | head -10
        echo "  Total files: $(find preprocessedT1scans23june -type f | wc -l)"
    else
        echo "  ❌ preprocessedT1scans23june directory not found"
    fi
    
    echo ""
    echo "📋 Hahmed environment directory:"
    if [ -d "hahmed" ]; then
        ls -la hahmed/ | head -5
    else
        echo "  ❌ hahmed directory not found"
    fi
}

# Function to setup conda environment
setup_conda_environment() {
    echo "🐍 Setting up conda environment..."
    
    # Load anaconda module
    echo "Loading anaconda3 module..."
    module load anaconda3
    
    if [ $? -eq 0 ]; then
        echo "✅ Anaconda3 module loaded successfully"
    else
        echo "❌ Failed to load anaconda3 module"
        return 1
    fi
    
    # Check available conda environments
    echo ""
    echo "📋 Available conda environments:"
    conda env list
    
    echo ""
    echo "🔍 Looking for existing environments in hahmed directory..."
    if [ -d "$PROJECT_DIR/hahmed" ]; then
        cd "$PROJECT_DIR/hahmed"
        
        # Look for conda environments or requirements files
        find . -name "*.yml" -o -name "requirements*.txt" -o -name "environment*.yml" | head -10
        
        # Check if there are any conda environment directories
        if [ -d ".conda" ] || [ -d "envs" ]; then
            echo "Found conda environment directories"
            ls -la .conda/ envs/ 2>/dev/null | head -10
        fi
    fi
    
    echo ""
    echo "💡 Next steps for environment setup:"
    echo "  1. Identify the correct conda environment name from hahmed directory"
    echo "  2. Activate it with: conda activate [environment_name]"
    echo "  3. Install additional packages for enhanced MCI training"
}

# Function to check system resources
check_system_resources() {
    echo "💻 Checking system resources..."
    
    echo "CPU Information:"
    lscpu | grep -E "Model name|CPU\(s\)|Thread"
    
    echo ""
    echo "Memory Information:"
    free -h
    
    echo ""
    echo "GPU Information:"
    if command -v nvidia-smi &> /dev/null; then
        nvidia-smi --query-gpu=name,memory.total,memory.free --format=csv,noheader,nounits
    else
        echo "  nvidia-smi not available (normal on login node)"
    fi
    
    echo ""
    echo "Available SLURM partitions:"
    sinfo -s 2>/dev/null || echo "  SLURM not available on login node"
}

# Function to create initial directory structure
create_directory_structure() {
    echo "📁 Creating enhanced MCI training directory structure..."
    
    WORK_DIR="$PROJECT_DIR/enhanced_mci_training"
    cd "$WORK_DIR" || return 1
    
    # Create subdirectories
    mkdir -p {scripts,data,models,logs,results,preprocessing}
    
    echo "✅ Created directory structure:"
    tree . 2>/dev/null || ls -la
}

# Main execution
main() {
    echo "🚀 Starting Illinois cluster setup..."
    echo ""
    
    # Check if we're on the cluster
    if ! check_cluster_environment; then
        exit 1
    fi
    
    # Setup project directory
    if ! setup_project_directory; then
        echo "❌ Failed to setup project directory"
        exit 1
    fi
    
    # Explore data structure
    explore_data_structure
    
    # Setup conda environment
    setup_conda_environment
    
    # Check system resources
    check_system_resources
    
    # Create directory structure
    create_directory_structure
    
    echo ""
    echo "✅ Illinois cluster setup completed!"
    echo ""
    echo "📋 Next steps:"
    echo "  1. Activate the correct conda environment"
    echo "  2. Run the data exploration script"
    echo "  3. Set up enhanced preprocessing pipeline"
    echo "  4. Create MCI dataset from NACC data"
    echo ""
    echo "🔧 Useful commands:"
    echo "  cd $PROJECT_DIR/enhanced_mci_training"
    echo "  module load anaconda3"
    echo "  conda activate [environment_name]"
    echo "  sbatch --account=sridhar-ic [job_script.sh]"
}

# Run main function
main "$@"
