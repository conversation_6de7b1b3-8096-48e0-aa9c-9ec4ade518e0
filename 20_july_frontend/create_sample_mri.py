#!/usr/bin/env python3
"""
Create sample MRI files for testing the Enhanced MCI Streamlit App
"""

import numpy as np
import nibabel as nib
from pathlib import Path

def create_realistic_brain_mri(shape=(91, 109, 91)):
    """Create a realistic-looking brain MRI scan"""
    
    # Create base brain structure
    mri_data = np.zeros(shape)
    
    # Add brain tissue (gray matter, white matter)
    center_z, center_y, center_x = shape[0]//2, shape[1]//2, shape[2]//2
    
    # Create brain outline (ellipsoid)
    z, y, x = np.ogrid[:shape[0], :shape[1], :shape[2]]
    brain_mask = ((z - center_z)**2 / (35**2) + 
                  (y - center_y)**2 / (45**2) + 
                  (x - center_x)**2 / (35**2)) <= 1
    
    # Add different tissue intensities
    mri_data[brain_mask] = 0.3  # Base brain tissue
    
    # Add white matter (higher intensity)
    white_matter_mask = ((z - center_z)**2 / (25**2) + 
                        (y - center_y)**2 / (35**2) + 
                        (x - center_x)**2 / (25**2)) <= 1
    mri_data[white_matter_mask] = 0.7
    
    # Add gray matter (medium intensity)
    gray_matter_thickness = 3
    gray_matter_mask = brain_mask & ~white_matter_mask
    mri_data[gray_matter_mask] = 0.5
    
    # Add ventricles (low intensity)
    ventricle_mask = ((z - center_z)**2 / (8**2) + 
                     (y - center_y)**2 / (12**2) + 
                     (x - center_x)**2 / (8**2)) <= 1
    mri_data[ventricle_mask] = 0.1
    
    # Add some noise for realism
    noise = np.random.normal(0, 0.05, shape)
    mri_data = np.clip(mri_data + noise, 0, 1)
    
    return mri_data

def create_sample_files():
    """Create sample MRI files for testing"""
    
    print("🧠 Creating sample MRI files for testing...")
    
    # Create samples directory
    samples_dir = Path('sample_mri_files')
    samples_dir.mkdir(exist_ok=True)
    
    # Sample 1: Normal Cognition (CN)
    print("📁 Creating CN (Normal) sample...")
    cn_mri = create_realistic_brain_mri()
    # Normal brain - no significant atrophy
    np.save(samples_dir / 'sample_cn_normal.npy', cn_mri)
    
    # Create NIfTI version
    cn_nii = nib.Nifti1Image(cn_mri, affine=np.eye(4))
    nib.save(cn_nii, samples_dir / 'sample_cn_normal.nii.gz')
    
    # Sample 2: Mild Cognitive Impairment (MCI)
    print("📁 Creating MCI sample...")
    mci_mri = create_realistic_brain_mri()
    # Add mild hippocampal atrophy
    center_z, center_y, center_x = mci_mri.shape[0]//2, mci_mri.shape[1]//2, mci_mri.shape[2]//2
    hippocampus_region = (
        slice(center_z-5, center_z+5),
        slice(center_y+15, center_y+25),
        slice(center_x-10, center_x+10)
    )
    mci_mri[hippocampus_region] *= 0.8  # Mild atrophy
    np.save(samples_dir / 'sample_mci_mild.npy', mci_mri)
    
    # Create NIfTI version
    mci_nii = nib.Nifti1Image(mci_mri, affine=np.eye(4))
    nib.save(mci_nii, samples_dir / 'sample_mci_mild.nii.gz')
    
    # Sample 3: Alzheimer's Disease (AD)
    print("📁 Creating AD (Alzheimer's) sample...")
    ad_mri = create_realistic_brain_mri()
    # Add significant atrophy
    center_z, center_y, center_x = ad_mri.shape[0]//2, ad_mri.shape[1]//2, ad_mri.shape[2]//2
    
    # Hippocampal atrophy
    hippocampus_region = (
        slice(center_z-8, center_z+8),
        slice(center_y+12, center_y+28),
        slice(center_x-12, center_x+12)
    )
    ad_mri[hippocampus_region] *= 0.5  # Significant atrophy
    
    # Cortical atrophy
    cortical_region = (
        slice(center_z-15, center_z+15),
        slice(center_y-20, center_y+20),
        slice(center_x-15, center_x+15)
    )
    ad_mri[cortical_region] *= 0.7  # Cortical thinning
    
    # Enlarged ventricles
    ventricle_region = (
        slice(center_z-12, center_z+12),
        slice(center_y-8, center_y+8),
        slice(center_x-12, center_x+12)
    )
    ad_mri[ventricle_region] = np.minimum(ad_mri[ventricle_region], 0.2)  # Enlarged ventricles
    
    np.save(samples_dir / 'sample_ad_severe.npy', ad_mri)
    
    # Create NIfTI version
    ad_nii = nib.Nifti1Image(ad_mri, affine=np.eye(4))
    nib.save(ad_nii, samples_dir / 'sample_ad_severe.nii.gz')
    
    print("✅ Sample files created successfully!")
    print(f"📂 Files saved in: {samples_dir.absolute()}")
    print("\n📋 Available samples:")
    for file_path in samples_dir.glob('*'):
        size_kb = file_path.stat().st_size / 1024
        print(f"   • {file_path.name} ({size_kb:.1f} KB)")
    
    return samples_dir

def main():
    """Main function"""
    print("🧠 Enhanced MCI App - Sample MRI Generator")
    print("=" * 50)
    
    try:
        samples_dir = create_sample_files()
        
        print("\n🚀 Ready to test!")
        print("1. Run: streamlit run enhanced_mci_streamlit_app.py")
        print("2. Upload any of the sample files from the sample_mri_files/ directory")
        print("3. Test with different age settings (50-90 years)")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating sample files: {e}")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
