#!/bin/bash

# Demetify NCOMMS2022 - Complete Standalone Installer
# This script installs Python, Conda, and all dependencies automatically

set -e  # Exit on any error

echo "🧠 Demetify NCOMMS2022 - Standalone Installer"
echo "=============================================="
echo "This will install Python, Conda, and all dependencies automatically."
echo "Installation may take 10-15 minutes depending on your internet connection."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running on supported OS
check_os() {
    print_status "Checking operating system..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        print_success "Linux detected"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        print_success "macOS detected"
    elif [[ "$OSTYPE" == "msys" ]] || [[ "$OSTYPE" == "cygwin" ]]; then
        OS="windows"
        print_success "Windows (WSL/Cygwin) detected"
    else
        print_error "Unsupported operating system: $OSTYPE"
        exit 1
    fi
}

# Install Miniconda if not present
install_miniconda() {
    print_status "Checking for Conda installation..."
    
    if command -v conda &> /dev/null; then
        print_success "Conda already installed"
        return 0
    fi
    
    print_status "Installing Miniconda..."
    
    # Create temp directory
    TEMP_DIR=$(mktemp -d)
    cd "$TEMP_DIR"
    
    # Download appropriate Miniconda installer
    if [[ "$OS" == "linux" ]]; then
        MINICONDA_URL="https://repo.anaconda.com/miniconda/Miniconda3-latest-Linux-x86_64.sh"
    elif [[ "$OS" == "macos" ]]; then
        MINICONDA_URL="https://repo.anaconda.com/miniconda/Miniconda3-latest-MacOSX-x86_64.sh"
    else
        print_error "Automatic Miniconda installation not supported on Windows"
        print_status "Please install Miniconda manually from: https://docs.conda.io/en/latest/miniconda.html"
        exit 1
    fi
    
    print_status "Downloading Miniconda installer..."
    curl -L -O "$MINICONDA_URL"
    
    # Install Miniconda
    INSTALLER_NAME=$(basename "$MINICONDA_URL")
    bash "$INSTALLER_NAME" -b -p "$HOME/miniconda3"
    
    # Initialize conda
    "$HOME/miniconda3/bin/conda" init bash
    
    # Source the conda setup
    source "$HOME/miniconda3/etc/profile.d/conda.sh"
    
    # Clean up
    cd - > /dev/null
    rm -rf "$TEMP_DIR"
    
    print_success "Miniconda installed successfully"
}

# Create and setup conda environment
setup_environment() {
    print_status "Setting up Demetify environment..."
    
    # Source conda
    if [[ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]]; then
        source "$HOME/miniconda3/etc/profile.d/conda.sh"
    elif [[ -f "/opt/miniconda3/etc/profile.d/conda.sh" ]]; then
        source "/opt/miniconda3/etc/profile.d/conda.sh"
    elif command -v conda &> /dev/null; then
        # Conda is already in PATH
        :
    else
        print_error "Could not find conda installation"
        exit 1
    fi
    
    # Create environment
    print_status "Creating 'demetify' conda environment..."
    conda create -n demetify python=3.9 -y
    
    # Activate environment
    conda activate demetify
    
    print_success "Environment created and activated"
}

# Install Python dependencies
install_dependencies() {
    print_status "Installing Python dependencies..."
    
    # Ensure we're in the right environment
    conda activate demetify
    
    # Install PyTorch with CUDA support (if available)
    print_status "Installing PyTorch..."
    conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia -y || \
    conda install pytorch torchvision torchaudio cpuonly -c pytorch -y
    
    # Install other dependencies
    print_status "Installing other dependencies..."
    pip install streamlit>=1.28.0
    pip install numpy>=1.24.0
    pip install pandas>=2.0.0
    pip install matplotlib>=3.7.0
    pip install scikit-learn>=1.3.0
    pip install scipy>=1.11.0
    pip install nibabel>=5.1.0
    pip install nilearn>=0.10.0
    pip install shap>=0.41.0
    pip install tqdm>=4.65.0
    pip install Pillow>=10.0.0
    
    print_success "All dependencies installed"
}

# Install FSL (optional)
install_fsl() {
    print_status "Checking for FSL installation..."
    
    if command -v fslreorient2std &> /dev/null; then
        print_success "FSL already installed"
        return 0
    fi
    
    print_warning "FSL not found. The system will use fallback preprocessing."
    print_status "For optimal performance, consider installing FSL manually:"
    print_status "https://fsl.fmrib.ox.ac.uk/fsl/fslwiki/FslInstallation"
    
    # Create a simple FSL check script
    cat > check_fsl.py << 'EOF'
import subprocess
import sys

def check_fsl():
    try:
        result = subprocess.run(['which', 'fslreorient2std'], capture_output=True, text=True)
        if result.returncode == 0:
            print("✓ FSL is available")
            return True
        else:
            print("⚠ FSL not found - using fallback preprocessing")
            return False
    except:
        print("⚠ FSL not found - using fallback preprocessing")
        return False

if __name__ == "__main__":
    check_fsl()
EOF
    
    python check_fsl.py
}

# Create launcher script
create_launcher() {
    print_status "Creating launcher script..."
    
    cat > launch_demetify.sh << 'EOF'
#!/bin/bash

# Demetify Launcher Script
echo "🧠 Starting Demetify NCOMMS2022..."

# Find conda installation
if [[ -f "$HOME/miniconda3/etc/profile.d/conda.sh" ]]; then
    source "$HOME/miniconda3/etc/profile.d/conda.sh"
elif [[ -f "/opt/miniconda3/etc/profile.d/conda.sh" ]]; then
    source "/opt/miniconda3/etc/profile.d/conda.sh"
elif command -v conda &> /dev/null; then
    eval "$(conda shell.bash hook)"
else
    echo "❌ Conda not found. Please run INSTALL_DEMETIFY.sh first."
    exit 1
fi

# Activate environment
conda activate demetify

# Check if activation was successful
if [[ "$CONDA_DEFAULT_ENV" != "demetify" ]]; then
    echo "❌ Failed to activate demetify environment"
    exit 1
fi

echo "✅ Environment activated: $CONDA_DEFAULT_ENV"
echo "🚀 Launching Demetify frontend..."
echo "   Access at: http://localhost:8501"
echo "   Press Ctrl+C to stop"
echo ""

# Launch Streamlit
streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address 0.0.0.0
EOF
    
    chmod +x launch_demetify.sh
    print_success "Launcher script created"
}

# Create Windows batch launcher
create_windows_launcher() {
    print_status "Creating Windows launcher..."
    
    cat > launch_demetify.bat << 'EOF'
@echo off
echo 🧠 Starting Demetify NCOMMS2022...

REM Find conda installation
if exist "%USERPROFILE%\miniconda3\Scripts\activate.bat" (
    call "%USERPROFILE%\miniconda3\Scripts\activate.bat" demetify
) else if exist "%USERPROFILE%\anaconda3\Scripts\activate.bat" (
    call "%USERPROFILE%\anaconda3\Scripts\activate.bat" demetify
) else if exist "C:\ProgramData\Miniconda3\Scripts\activate.bat" (
    call "C:\ProgramData\Miniconda3\Scripts\activate.bat" demetify
) else (
    echo ❌ Conda not found. Please install Miniconda first.
    pause
    exit /b 1
)

echo ✅ Environment activated
echo 🚀 Launching Demetify frontend...
echo    Access at: http://localhost:8501
echo    Press Ctrl+C to stop
echo.

REM Launch Streamlit
streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address 0.0.0.0

pause
EOF
    
    print_success "Windows launcher created"
}

# Run tests
run_tests() {
    print_status "Running system tests..."
    
    conda activate demetify
    
    if python test_ncomms2022_system.py; then
        print_success "All tests passed!"
    else
        print_warning "Some tests failed, but the system should still work"
    fi
}

# Main installation process
main() {
    echo "Starting installation process..."
    echo ""
    
    check_os
    install_miniconda
    setup_environment
    install_dependencies
    install_fsl
    create_launcher
    create_windows_launcher
    
    print_status "Running final tests..."
    if run_tests; then
        echo ""
        print_success "🎉 Installation completed successfully!"
        echo ""
        echo "To launch Demetify:"
        echo "  Linux/Mac: ./launch_demetify.sh"
        echo "  Windows:   launch_demetify.bat"
        echo ""
        echo "Or manually:"
        echo "  conda activate demetify"
        echo "  streamlit run demetify_ncomms2022_app.py"
        echo ""
        print_success "Demetify is ready to use!"
    else
        print_warning "Installation completed with warnings. Check the logs above."
    fi
}

# Run main function
main "$@"
