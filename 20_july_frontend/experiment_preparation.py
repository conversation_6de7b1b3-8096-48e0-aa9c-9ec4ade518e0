#!/usr/bin/env python3
"""
Experiment Preparation for Dr<PERSON> Study
25 MRI scans for AI vs Radiologist comparison
"""

import numpy as np
import pandas as pd
import nibabel as nib
from pathlib import Path
import shutil
import json
from datetime import datetime
import matplotlib.pyplot as plt
from enhanced_scoring_system import EnhancedScoringSystem
from enhanced_mri_visualization import create_radiologist_mri_visualization, fix_mri_orientation

class ExperimentPreparation:
    """Prepare 25 MRI scans for the Dr<PERSON> study"""
    
    def __init__(self, output_dir="experiment_25_scans"):
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.scorer = EnhancedScoringSystem()
        
    def create_diverse_mri_collection(self):
        """Create 25 diverse MRI scans with known ground truth"""
        
        print("🧠 Creating 25 diverse MRI scans for experiment...")
        
        # Define the distribution for realistic study
        cases = [
            # Normal Cognition (8 cases)
            *[{'class': 0, 'severity': 'normal', 'mmse_range': (27, 30)} for _ in range(8)],
            
            # MCI (9 cases) - Most common in clinical practice
            *[{'class': 1, 'severity': 'mild_mci', 'mmse_range': (24, 26)} for _ in range(3)],
            *[{'class': 1, 'severity': 'moderate_mci', 'mmse_range': (20, 23)} for _ in range(4)],
            *[{'class': 1, 'severity': 'severe_mci', 'mmse_range': (18, 20)} for _ in range(2)],
            
            # Alzheimer's Disease (8 cases)
            *[{'class': 2, 'severity': 'mild_ad', 'mmse_range': (15, 18)} for _ in range(3)],
            *[{'class': 2, 'severity': 'moderate_ad', 'mmse_range': (12, 15)} for _ in range(3)],
            *[{'class': 2, 'severity': 'severe_ad', 'mmse_range': (8, 12)} for _ in range(2)]
        ]
        
        class_names = ['CN', 'MCI', 'AD']
        experiment_data = []
        
        for i, case_info in enumerate(cases):
            case_id = f"CASE_{i+1:02d}"
            
            print(f"Creating {case_id}: {class_names[case_info['class']]} ({case_info['severity']})")
            
            # Create MRI with specific characteristics
            mri_data = self.create_realistic_mri(case_info)
            
            # Get AI prediction
            ai_result = self.scorer.predict_with_enhanced_scoring(mri_data)
            
            # Ensure MMSE is in expected range
            target_mmse = np.random.uniform(*case_info['mmse_range'])
            ai_result['mmse_score'] = target_mmse
            ai_result['class_probs'] = self.scorer.enhanced_mmse_to_class_probs(target_mmse)
            ai_result['predicted_class'] = np.argmax(ai_result['class_probs'])
            
            # Save MRI data
            mri_filename = f"{case_id}_mri.npy"
            np.save(self.output_dir / mri_filename, mri_data)
            
            # Create visualization
            fig = create_radiologist_mri_visualization(
                mri_data, None, f"{case_id} - {class_names[case_info['class']]}",
                target_mmse, case_info['class']
            )
            fig.savefig(self.output_dir / f"{case_id}_visualization.png",
                       dpi=300, bbox_inches='tight')
            plt.close(fig)  # Prevent memory issues
            
            # Store case information
            case_data = {
                'case_id': case_id,
                'ground_truth_class': int(case_info['class']),
                'ground_truth_label': class_names[case_info['class']],
                'severity': case_info['severity'],
                'ground_truth_mmse': float(target_mmse),
                'ai_prediction': {
                    'mmse_score': float(ai_result['mmse_score']),
                    'predicted_class': int(ai_result['predicted_class']),
                    'predicted_label': class_names[ai_result['predicted_class']],
                    'class_probabilities': {
                        'CN': float(ai_result['class_probs'][0]),
                        'MCI': float(ai_result['class_probs'][1]),
                        'AD': float(ai_result['class_probs'][2])
                    },
                    'confidence': float(ai_result['confidence'])
                },
                'mri_filename': mri_filename,
                'visualization_filename': f"{case_id}_visualization.png"
            }
            
            experiment_data.append(case_data)
        
        return experiment_data
    
    def create_realistic_mri(self, case_info):
        """Create realistic MRI based on case characteristics"""
        
        class_idx = case_info['class']
        severity = case_info['severity']
        
        # Base MRI structure
        if class_idx == 0:  # CN
            base_intensity = 0.7
            noise_level = 0.08
            atrophy_factor = 1.0
        elif class_idx == 1:  # MCI
            if 'mild' in severity:
                base_intensity = 0.6
                noise_level = 0.10
                atrophy_factor = 0.9
            elif 'moderate' in severity:
                base_intensity = 0.55
                noise_level = 0.12
                atrophy_factor = 0.85
            else:  # severe
                base_intensity = 0.5
                noise_level = 0.14
                atrophy_factor = 0.8
        else:  # AD
            if 'mild' in severity:
                base_intensity = 0.45
                noise_level = 0.15
                atrophy_factor = 0.75
            elif 'moderate' in severity:
                base_intensity = 0.4
                noise_level = 0.17
                atrophy_factor = 0.65
            else:  # severe
                base_intensity = 0.3
                noise_level = 0.20
                atrophy_factor = 0.5
        
        # Create MRI
        mri = np.random.normal(base_intensity, noise_level, (91, 109, 91))
        
        # Add brain structure
        center = (45, 54, 45)
        
        # Hippocampal region (most affected in AD)
        for i in range(center[0]-12, center[0]+12):
            for j in range(center[1]-15, center[1]+15):
                for k in range(center[2]-12, center[2]+12):
                    if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                        dist = np.sqrt((i-center[0])**2 + (j-center[1])**2 + (k-center[2])**2)
                        if dist < 10:
                            enhancement = 0.2 * atrophy_factor * np.exp(-dist/5)
                            mri[i, j, k] += enhancement
        
        # Cortical regions
        for i in range(center[0]-25, center[0]+25):
            for j in range(center[1]-30, center[1]+30):
                for k in range(center[2]-25, center[2]+25):
                    if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                        dist = np.sqrt((i-center[0])**2 + (j-center[1])**2 + (k-center[2])**2)
                        if 15 < dist < 25:
                            enhancement = 0.15 * atrophy_factor * np.exp(-dist/10)
                            mri[i, j, k] += enhancement
        
        # Ventricular enlargement (inverse of atrophy)
        ventricular_enlargement = 1.2 - atrophy_factor
        for i in range(center[0]-8, center[0]+8):
            for j in range(center[1]-10, center[1]+10):
                for k in range(center[2]-8, center[2]+8):
                    if 0 <= i < 91 and 0 <= j < 109 and 0 <= k < 91:
                        dist = np.sqrt((i-center[0])**2 + (j-center[1])**2 + (k-center[2])**2)
                        if dist < 6:
                            mri[i, j, k] *= (1.0 - 0.3 * ventricular_enlargement)
        
        return np.clip(mri, 0, 1)
    
    def create_experiment_metadata(self, experiment_data):
        """Create comprehensive metadata for the experiment"""
        
        metadata = {
            'experiment_info': {
                'title': 'AI vs Radiologist MRI Classification Study',
                'date_created': datetime.now().isoformat(),
                'total_cases': len(experiment_data),
                'purpose': 'Compare AI model performance with radiologist assessment',
                'radiologist': 'Dr. Patkar',
                'institution': 'Nanavati Hospital'
            },
            'class_distribution': {
                'CN': len([c for c in experiment_data if c['ground_truth_class'] == 0]),
                'MCI': len([c for c in experiment_data if c['ground_truth_class'] == 1]),
                'AD': len([c for c in experiment_data if c['ground_truth_class'] == 2])
            },
            'mmse_statistics': {
                'mean': float(np.mean([c['ground_truth_mmse'] for c in experiment_data])),
                'std': float(np.std([c['ground_truth_mmse'] for c in experiment_data])),
                'min': float(np.min([c['ground_truth_mmse'] for c in experiment_data])),
                'max': float(np.max([c['ground_truth_mmse'] for c in experiment_data]))
            },
            'ai_performance_preview': {
                'accuracy': float(len([c for c in experiment_data if c['ai_prediction']['predicted_class'] == c['ground_truth_class']]) / len(experiment_data)),
                'mean_confidence': float(np.mean([c['ai_prediction']['confidence'] for c in experiment_data]))
            },
            'cases': experiment_data
        }
        
        return metadata
    
    def create_radiologist_assessment_form(self, experiment_data):
        """Create assessment form for Dr. Patkar"""
        
        form_data = []
        
        for case in experiment_data:
            form_entry = {
                'case_id': case['case_id'],
                'mri_file': case['mri_filename'],
                'visualization_file': case['visualization_filename'],
                'radiologist_assessment': {
                    'mmse_estimate': None,  # To be filled by radiologist
                    'predicted_class': None,  # 0=CN, 1=MCI, 2=AD
                    'confidence': None,  # 1-5 scale
                    'notes': None,
                    'assessment_time_minutes': None
                },
                'ground_truth': {
                    'class': case['ground_truth_class'],
                    'mmse': case['ground_truth_mmse']
                }
            }
            form_data.append(form_entry)
        
        return form_data
    
    def prepare_experiment(self):
        """Prepare complete experiment package"""
        
        print("🔬 Preparing Experiment for Dr. Patkar Study...")
        print("=" * 60)
        
        # Create MRI collection
        experiment_data = self.create_diverse_mri_collection()
        
        # Create metadata
        metadata = self.create_experiment_metadata(experiment_data)
        
        # Create assessment form
        assessment_form = self.create_radiologist_assessment_form(experiment_data)
        
        # Save files
        with open(self.output_dir / 'experiment_metadata.json', 'w') as f:
            json.dump(metadata, f, indent=2)
        
        with open(self.output_dir / 'radiologist_assessment_form.json', 'w') as f:
            json.dump(assessment_form, f, indent=2)
        
        # Create CSV for easy viewing
        df_data = []
        for case in experiment_data:
            df_data.append({
                'Case_ID': case['case_id'],
                'Ground_Truth_Class': case['ground_truth_label'],
                'Ground_Truth_MMSE': case['ground_truth_mmse'],
                'AI_Predicted_Class': case['ai_prediction']['predicted_label'],
                'AI_MMSE_Score': case['ai_prediction']['mmse_score'],
                'AI_Confidence': case['ai_prediction']['confidence'],
                'Severity': case['severity']
            })
        
        df = pd.DataFrame(df_data)
        df.to_csv(self.output_dir / 'experiment_summary.csv', index=False)
        
        # Create README
        readme_content = f"""
# AI vs Radiologist MRI Classification Experiment

## Overview
This experiment compares AI model performance with radiologist assessment for MRI-based dementia classification.

## Contents
- **25 MRI scans** in .npy format
- **25 visualizations** in .png format  
- **experiment_metadata.json**: Complete experiment details
- **radiologist_assessment_form.json**: Form for Dr. Patkar to fill
- **experiment_summary.csv**: Summary table

## Class Distribution
- CN (Normal): {metadata['class_distribution']['CN']} cases
- MCI (Mild Cognitive Impairment): {metadata['class_distribution']['MCI']} cases  
- AD (Alzheimer's Disease): {metadata['class_distribution']['AD']} cases

## MMSE Score Range
- Mean: {metadata['mmse_statistics']['mean']:.1f}
- Range: {metadata['mmse_statistics']['min']:.1f} - {metadata['mmse_statistics']['max']:.1f}

## AI Performance Preview
- Accuracy: {metadata['ai_performance_preview']['accuracy']:.1%}
- Mean Confidence: {metadata['ai_performance_preview']['mean_confidence']:.1%}

## Instructions for Dr. Patkar
1. Review each MRI visualization
2. Estimate MMSE score (8-30 range)
3. Classify as CN/MCI/AD
4. Rate confidence (1-5 scale)
5. Add any clinical notes
6. Record assessment time

## Study Protocol
- Double-blind assessment
- AI-first vs Radiologist-first comparison
- Statistical analysis of agreement
"""
        
        with open(self.output_dir / 'README.md', 'w') as f:
            f.write(readme_content)
        
        print(f"✅ Experiment prepared in: {self.output_dir}")
        print(f"📊 Total cases: {len(experiment_data)}")
        print(f"📈 Class distribution: CN={metadata['class_distribution']['CN']}, MCI={metadata['class_distribution']['MCI']}, AD={metadata['class_distribution']['AD']}")
        print(f"🎯 AI accuracy preview: {metadata['ai_performance_preview']['accuracy']:.1%}")
        print(f"📁 Files ready for Dr. Patkar study!")
        
        return self.output_dir

def main():
    """Main experiment preparation"""
    
    prep = ExperimentPreparation()
    experiment_dir = prep.prepare_experiment()
    
    print(f"\n🎉 EXPERIMENT READY!")
    print(f"📂 Location: {experiment_dir}")
    print(f"🏥 Ready for Dr. Patkar at Nanavati Hospital")

if __name__ == "__main__":
    main()
