# MRI-based Alzheimer's Disease Assessment Frontend

A Streamlit-based web application for MRI-based dementia classification using advanced deep learning models.

## 🧠 Overview

This frontend provides an intuitive interface for:
- **MRI Upload & Preprocessing**: Support for .nii and .npy files with automatic preprocessing
- **AI-Powered Analysis**: Two-task prediction system (ADD classification + COG regression)
- **Clinical Visualization**: Medical-grade results display with confidence scores
- **Model Management**: Easy selection between different cross-validation folds

## 🚀 Features

### Core Functionality
- **File Upload**: Drag-and-drop interface for MRI scans (.nii/.npy formats)
- **Preprocessing Pipeline**: Automatic skull stripping, normalization, and resampling to (121,145,121)
- **Model Selection**: Choose from available pretrained models (cross-validation folds)
- **Real-time Inference**: GPU/CPU-accelerated predictions
- **Results Visualization**: Interactive charts and clinical summaries

### Medical Interface Design
- **Professional Theme**: Clean, medical-grade interface design
- **Confidence Indicators**: Color-coded results with probability breakdowns
- **Clinical Summary**: Automated interpretation of results
- **Safety Disclaimers**: Clear research-only usage warnings

## 📋 Requirements

### System Requirements
- Python 3.8+
- 4GB+ RAM (8GB+ recommended)
- Optional: CUDA-compatible GPU for faster inference

### Dependencies
Install the required packages:
```bash
pip install -r requirements_ncomms2022.txt
```

### Model Files
The application requires the ncomms2022 pretrained models:
1. Clone the ncomms2022 repository (already done)
2. Ensure `ncomms2022/checkpoint_dir/` contains the pretrained weights
3. Required files per model: `backbone_58.pth`, `ADD_58.pth`, `COG_58.pth`

## 🏃‍♂️ Quick Start

1. **Install Dependencies**:
   ```bash
   pip install -r requirements_ncomms2022.txt
   ```

2. **Launch the Application**:
   ```bash
   streamlit run ncomms2022_frontend.py
   ```

3. **Access the Interface**:
   - Open your browser to `http://localhost:8501`
   - The application will automatically load

## 📖 Usage Guide

### Step 1: Model Setup
1. Select a pretrained model from the sidebar dropdown
2. Choose compute device (CPU/GPU)
3. Click "Load Model" to initialize

### Step 2: MRI Upload
1. Upload an MRI scan (.nii or .npy format)
2. Configure preprocessing options (skull stripping recommended for .nii files)
3. Click "Preprocess MRI" to prepare the data

### Step 3: Analysis
1. Click "Run Dementia Assessment" to perform AI analysis
2. View results in real-time with confidence scores
3. Review clinical summary and recommendations

### Step 4: Interpretation
- **ADD Task**: Binary classification (Normal vs Alzheimer's Disease)
- **COG Task**: Cognitive impairment score (0-3 scale)
- **Clinical Summary**: Automated risk assessment

## 🔧 Technical Details

### Model Architecture
- **Backbone**: 3D CNN with ResNet-like architecture
- **Tasks**: Multi-task learning (ADD classification + COG regression)
- **Input**: (1, 1, 121, 145, 121) preprocessed MRI volumes
- **Output**: ADD probabilities + COG continuous score

### Preprocessing Pipeline
1. **File Loading**: Support for NIfTI (.nii) and NumPy (.npy) formats
2. **Skull Stripping**: Automatic brain extraction (optional)
3. **Intensity Normalization**: Percentile-based normalization
4. **Spatial Resampling**: Resize to target shape (121, 145, 121)
5. **Validation**: Data quality checks before inference

### Available Models
- `CNN_baseline_new_cross0` through `cross4`: Main baseline models
- `CNN_3ways_special1_cross0` through `cross4`: Specialized variants
- Additional fusion and experimental models

## 📊 Results Interpretation

### ADD Classification
- **Normal (0)**: No significant Alzheimer's pathology detected
- **AD (1)**: Alzheimer's disease pathology detected
- **Confidence**: Probability of the predicted class

### COG Regression Score
- **0.0 - 0.5**: Normal cognition
- **0.5 - 1.5**: Mild cognitive impairment
- **1.5 - 3.0**: Significant cognitive impairment

### Clinical Risk Assessment
- **Normal**: Both assessments indicate normal function
- **Mild Concern**: Minor cognitive changes detected
- **Moderate Risk**: One assessment indicates impairment
- **High Risk**: Both assessments indicate significant impairment

## ⚠️ Important Disclaimers

1. **Research Use Only**: This tool is for research and educational purposes only
2. **Not for Clinical Diagnosis**: Do not use for medical diagnosis or treatment decisions
3. **Professional Consultation**: Always consult qualified healthcare professionals
4. **Data Privacy**: Ensure compliance with local data protection regulations

## 🔬 Scientific Background

This frontend implements advanced deep learning models for MRI-based cognitive assessment. The models demonstrate strong performance in distinguishing between:
- Normal cognition (NC)
- Mild cognitive impairment (MCI)
- Alzheimer's disease dementia (AD)
- Non-Alzheimer's dementia (nADD)

## 🛠️ Development

### File Structure
```
├── ncomms2022_frontend.py          # Main Streamlit application
├── ncomms2022_preprocessing.py     # MRI preprocessing pipeline
├── ncomms2022_model.py            # Model wrapper and management
├── requirements_ncomms2022.txt    # Python dependencies
├── models.py                      # Core model architectures (from ncomms2022)
├── utils.py                       # Utility functions (from ncomms2022)
├── backends/                      # Neural network backends (from ncomms2022)
├── task_config.json              # Model configuration (from ncomms2022)
└── ncomms2022/                    # Original repository with pretrained weights
```

### Customization
- Modify `ncomms2022_preprocessing.py` for custom preprocessing pipelines
- Update `ncomms2022_model.py` to add new model architectures
- Customize the UI in `ncomms2022_frontend.py` for specific use cases

## 📞 Support

For technical issues or questions:
1. Check the original ncomms2022 repository documentation
2. Verify all dependencies are correctly installed
3. Ensure pretrained model files are available
4. Review the Streamlit logs for detailed error messages

## 📄 License

This frontend follows the same license as the original ncomms2022 repository. Please refer to the original repository for licensing details.
