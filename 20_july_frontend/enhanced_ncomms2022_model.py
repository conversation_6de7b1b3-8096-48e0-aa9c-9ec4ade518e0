"""
Enhanced ncomms2022 Model with MCI Support and Continuous Atrophy Scoring
Supports CN/MCI/AD classification with comprehensive clinical feature extraction
"""

import os
import torch
import torch.nn as nn
import numpy as np
import pandas as pd
from pathlib import Path
import streamlit as st
import json
from typing import Dict, List, Tuple, Optional

# Import the model components
from models import _CNN_Bone, MLP, Model
from utils import read_json

class EnhancedMultiTaskModel(nn.Module):
    """
    Enhanced multi-task model for comprehensive dementia assessment
    Supports continuous atrophy scoring and MCI classification
    """
    def __init__(self, backbone, mlp_dict, tasks):
        super(EnhancedMultiTaskModel, self).__init__()
        self.backbone = backbone
        self.mlp_dict = nn.ModuleDict(mlp_dict)
        self.tasks = tasks
        
        # Add attention mechanism for better feature extraction
        self.attention = nn.MultiheadAttention(embed_dim=backbone.size, num_heads=8)
        self.layer_norm = nn.LayerNorm(backbone.size)

    def forward(self, x):
        # Extract features using backbone
        features = self.backbone(x)
        
        # Apply attention mechanism
        features_reshaped = features.unsqueeze(0)  # Add sequence dimension
        attended_features, _ = self.attention(features_reshaped, features_reshaped, features_reshaped)
        attended_features = self.layer_norm(attended_features.squeeze(0) + features)
        
        # Apply task-specific MLPs
        outputs = {}
        for task in self.tasks:
            if task in self.mlp_dict:
                outputs[task] = self.mlp_dict[task](attended_features)
        
        return outputs

class EnhancedNCOMMs2022Model:
    """
    Enhanced wrapper for ncomms2022 model with MCI support and clinical features
    """
    
    def __init__(self, device='cpu'):
        self.device = device if torch.cuda.is_available() else 'cpu'
        self.tasks = ['ATROPHY_CONTINUOUS', 'DEMENTIA_CLASSIFICATION', 'COG_SCORE', 
                     'REGIONAL_ATROPHY', 'CLINICAL_SCORES']
        self.model = None
        self.backbone = None
        self.MLPs = {}
        self.task_config = None
        self.model_loaded = False
        
        # Load enhanced task configuration
        self.load_task_config()
        
    def load_task_config(self):
        """Load enhanced task configuration"""
        try:
            config_path = 'enhanced_task_config.json'
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    self.task_config = json.load(f)
                st.success("✅ Enhanced task configuration loaded successfully")
            else:
                # Fallback to original config
                with open('task_config.json', 'r') as f:
                    self.task_config = json.load(f)
                st.warning("⚠️ Using original task configuration - enhanced features not available")
        except Exception as e:
            st.error(f"❌ Error loading task configuration: {str(e)}")
            self._create_fallback_config()
    
    def _create_fallback_config(self):
        """Create fallback configuration if files are missing"""
        self.task_config = {
            "backbone": {"model": "CNN", "fil_num": 20, "drop_rate": 0, "lr": 0.001, "epochs": 101},
            "ATROPHY_CONTINUOUS": {"fil_num": 100, "drop_rate": 0.5, "out_size": 1, "type": "reg"},
            "DEMENTIA_CLASSIFICATION": {"fil_num": 50, "drop_rate": 0.5, "out_size": 3, "type": "cla"}
        }
    
    def initialize_model(self):
        """Initialize the enhanced model architecture"""
        try:
            # Initialize backbone CNN
            backbone_config = self.task_config['backbone']
            self.backbone = _CNN_Bone(backbone_config)
            
            # Initialize task-specific MLPs
            for task in self.tasks:
                if task in self.task_config:
                    task_config = self.task_config[task]
                    self.MLPs[task] = MLP(self.backbone.size, task_config)
            
            # Create multi-task model
            self.model = EnhancedMultiTaskModel(self.backbone, self.MLPs, self.tasks)
            self.model.to(self.device)
            
            st.success(f"✅ Enhanced model initialized with {len(self.tasks)} tasks")
            return True
            
        except Exception as e:
            st.error(f"❌ Error initializing enhanced model: {str(e)}")
            return False
    
    def predict_single(self, mri_data: np.ndarray) -> Dict:
        """
        Enhanced prediction with comprehensive clinical assessment
        
        Args:
            mri_data: Preprocessed MRI data (182, 218, 182)
            
        Returns:
            Dict containing all predictions and clinical scores
        """
        if not self.model_loaded:
            st.error("❌ Model not loaded")
            return None
        
        try:
            # Prepare input tensor
            input_tensor = torch.from_numpy(mri_data).float()
            input_tensor = input_tensor.unsqueeze(0).unsqueeze(0)  # Add batch and channel dims
            input_tensor = input_tensor.to(self.device)
            
            # Forward pass
            self.model.eval()
            with torch.no_grad():
                outputs = self.model(input_tensor)
            
            # Process outputs
            results = self._process_predictions(outputs)
            
            return results
            
        except Exception as e:
            st.error(f"❌ Prediction failed: {str(e)}")
            return None
    
    def _process_predictions(self, outputs: Dict) -> Dict:
        """Process model outputs into clinical results"""
        results = {}
        
        # Continuous atrophy score
        if 'ATROPHY_CONTINUOUS' in outputs:
            atrophy_score = torch.sigmoid(outputs['ATROPHY_CONTINUOUS']).cpu().numpy()[0, 0]
            results['atrophy_score'] = float(atrophy_score)
            results['atrophy_category'] = self._categorize_atrophy(atrophy_score)
        
        # Dementia classification
        if 'DEMENTIA_CLASSIFICATION' in outputs:
            class_probs = torch.softmax(outputs['DEMENTIA_CLASSIFICATION'], dim=1).cpu().numpy()[0]
            results['class_probabilities'] = {
                'CN': float(class_probs[0]),
                'MCI': float(class_probs[1]),
                'AD': float(class_probs[2])
            }
            results['predicted_class'] = ['CN', 'MCI', 'AD'][np.argmax(class_probs)]
            results['confidence'] = float(np.max(class_probs))
        
        # Cognitive score
        if 'COG_SCORE' in outputs:
            cog_score = outputs['COG_SCORE'].cpu().numpy()[0, 0]
            results['cog_score'] = float(np.clip(cog_score, 0, 3))
        
        # Regional atrophy scores
        if 'REGIONAL_ATROPHY' in outputs:
            regional_scores = torch.sigmoid(outputs['REGIONAL_ATROPHY']).cpu().numpy()[0]
            regions = ["hippocampus", "temporal_cortex", "parietal_cortex", 
                      "frontal_cortex", "occipital_cortex", "subcortical"]
            results['regional_atrophy'] = {
                region: float(score) for region, score in zip(regions, regional_scores)
            }
        
        # Clinical scores
        if 'CLINICAL_SCORES' in outputs:
            clinical_raw = outputs['CLINICAL_SCORES'].cpu().numpy()[0]
            results['clinical_scores'] = {
                'MTA_score': float(np.clip(clinical_raw[0], 0, 4)),
                'GCA_score': float(np.clip(clinical_raw[1], 0, 3)),
                'Koedam_score': float(np.clip(clinical_raw[2], 0, 3)),
                'Fazekas_score': float(np.clip(clinical_raw[3], 0, 3))
            }
        
        return results
    
    def _categorize_atrophy(self, score: float) -> str:
        """Categorize continuous atrophy score"""
        if score < 0.33:
            return "Normal Cognition"
        elif score < 0.67:
            return "Mild Cognitive Impairment"
        else:
            return "Alzheimer's Disease"
    
    def get_model_info(self) -> Dict:
        """Get enhanced model information"""
        if not self.model_loaded:
            return {"status": "Model not loaded"}
        
        return {
            "Model Type": "Enhanced ncomms2022 with MCI Support",
            "Tasks": len(self.tasks),
            "Device": self.device,
            "Input Shape": "(182, 218, 182)",
            "Features": [
                "Continuous atrophy scoring",
                "3-class classification (CN/MCI/AD)",
                "Regional atrophy assessment",
                "Clinical score automation",
                "Attention-enhanced features"
            ]
        }

class EnhancedModelManager:
    """Enhanced model manager with MCI support"""
    
    def __init__(self):
        self.available_models = []
        self.scan_for_models()
    
    def scan_for_models(self):
        """Scan for available enhanced model checkpoints"""
        checkpoint_dir = Path("ncomms2022/checkpoint_dir")
        if checkpoint_dir.exists():
            self.available_models = [d.name for d in checkpoint_dir.iterdir() if d.is_dir()]
        else:
            st.warning("⚠️ No checkpoint directory found")
    
    def get_available_models(self) -> List[str]:
        """Get list of available models"""
        return self.available_models
    
    def load_model(self, model_name: str, device: str = 'cpu') -> Optional[EnhancedNCOMMs2022Model]:
        """Load enhanced model with MCI support"""
        try:
            model = EnhancedNCOMMs2022Model(device=device)
            
            if model.initialize_model():
                # Load pretrained weights if available
                checkpoint_path = Path(f"ncomms2022/checkpoint_dir/{model_name}")
                if checkpoint_path.exists():
                    # Load weights (implementation depends on checkpoint format)
                    st.info(f"📂 Loading weights from {model_name}")
                
                model.model_loaded = True
                return model
            else:
                return None
                
        except Exception as e:
            st.error(f"❌ Failed to load enhanced model: {str(e)}")
            return None
