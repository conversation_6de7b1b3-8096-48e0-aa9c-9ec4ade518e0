"""
SHAP Interpretability Component for ncomms2022 Model
Generates SHAP heatmaps and overlays for MRI scans
"""

import numpy as np
import torch
import torch.nn as nn
from typing import Union, Tuple, Optional, Dict
import logging
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from pathlib import Path

# Try to import SHAP
try:
    import shap
    SHAP_AVAILABLE = True
except ImportError:
    SHAP_AVAILABLE = False
    logging.warning("SHAP not available. Install with: pip install shap")

# Import model components
from ncomms2022_model_enhanced import NCOMMSClassifier, Model

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NCOMMSSHAPExplainer:
    """
    SHAP-based interpretability for ncomms2022 models
    """
    
    def __init__(self, 
                 classifier: NCOMMSClassifier,
                 background_samples: Optional[np.ndarray] = None,
                 num_background: int = 10):
        """
        Initialize SHAP explainer
        
        Args:
            classifier: Trained NCOMMSClassifier instance
            background_samples: Background samples for SHAP. If None, generates random samples
            num_background: Number of background samples to use
        """
        self.classifier = classifier
        self.device = classifier.device
        self.num_background = num_background
        
        if not SHAP_AVAILABLE:
            raise ImportError("SHAP is required for interpretability. Install with: pip install shap")
        
        # Initialize background samples
        if background_samples is None:
            self.background_samples = self._generate_background_samples()
        else:
            self.background_samples = background_samples
        
        # Initialize explainers
        self.add_explainer = None
        self.cog_explainer = None
        self.use_gradients = False
        self._initialize_explainers()
    
    def _generate_background_samples(self) -> torch.Tensor:
        """Generate random background samples"""
        logger.info(f"Generating {self.num_background} random background samples")
        
        # Generate random samples with similar statistics to real MRI data
        background = torch.randn(self.num_background, 1, 182, 218, 182) * 0.5 + 1.0
        background = torch.clamp(background, 0, 8)  # Clip to expected range
        
        return background.to(self.device)
    
    def _initialize_explainers(self):
        """Initialize SHAP explainers for both models"""
        try:
            # Create wrapper functions for SHAP that avoid the sum check issue
            def add_model_wrapper(x):
                with torch.no_grad():
                    features = self.classifier.backbone(x)
                    output = self.classifier.add_mlp(features)
                    return torch.softmax(output, dim=1)

            def cog_model_wrapper(x):
                with torch.no_grad():
                    features = self.classifier.backbone(x)
                    output = self.classifier.cog_mlp(features)
                    return output

            if self.classifier.backbone is not None and self.classifier.add_mlp is not None:
                logger.info("Initializing SHAP explainer for ADD model...")
                self.add_explainer = shap.DeepExplainer(
                    add_model_wrapper,
                    self.background_samples
                )
                logger.info("ADD explainer initialized successfully")

            if self.classifier.backbone is not None and self.classifier.cog_mlp is not None:
                logger.info("Initializing SHAP explainer for COG model...")
                self.cog_explainer = shap.DeepExplainer(
                    cog_model_wrapper,
                    self.background_samples
                )
                logger.info("COG explainer initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing SHAP explainers: {e}")
            # Fallback: use gradient-based explanations
            logger.info("Falling back to gradient-based explanations...")
            self.use_gradients = True
    
    def _gradient_explanation(self, mri_data: np.ndarray, task: str = 'ADD') -> np.ndarray:
        """
        Generate gradient-based explanation as fallback

        Args:
            mri_data: Preprocessed MRI data (182, 218, 182)
            task: 'ADD' or 'COG'

        Returns:
            Gradient-based saliency map
        """
        try:
            # Prepare input tensor
            if len(mri_data.shape) == 3:
                input_tensor = torch.from_numpy(mri_data).float().unsqueeze(0).unsqueeze(0)
            else:
                input_tensor = torch.from_numpy(mri_data).float()

            input_tensor = input_tensor.to(self.device)
            input_tensor.requires_grad_(True)

            # Forward pass
            features = self.classifier.backbone(input_tensor)

            if task == 'ADD':
                output = self.classifier.add_mlp(features)
                # Focus on AD class (index 1)
                target = torch.softmax(output, dim=1)[0, 1]
            else:
                output = self.classifier.cog_mlp(features)
                target = output[0, 0]

            # Backward pass
            target.backward()

            # Get gradients
            gradients = input_tensor.grad.squeeze().cpu().numpy()

            # Take absolute value for saliency
            saliency = np.abs(gradients)

            return saliency

        except Exception as e:
            logger.error(f"Error generating gradient explanation: {e}")
            raise

    def explain_add_prediction(self, mri_data: np.ndarray) -> np.ndarray:
        """
        Generate SHAP explanation for ADD prediction

        Args:
            mri_data: Preprocessed MRI data (182, 218, 182)

        Returns:
            SHAP values for AD class (182, 218, 182)
        """
        if self.use_gradients or self.add_explainer is None:
            logger.info("Using gradient-based explanation for ADD prediction...")
            return self._gradient_explanation(mri_data, 'ADD')

        try:
            # Prepare input tensor
            if len(mri_data.shape) == 3:
                input_tensor = torch.from_numpy(mri_data).float().unsqueeze(0).unsqueeze(0)
            else:
                input_tensor = torch.from_numpy(mri_data).float()

            input_tensor = input_tensor.to(self.device)

            # Generate SHAP values
            logger.info("Generating SHAP values for ADD prediction...")
            shap_values = self.add_explainer.shap_values(input_tensor)

            # Extract SHAP values for AD class (index 1)
            if isinstance(shap_values, list):
                ad_shap = shap_values[1]  # AD class
            else:
                ad_shap = shap_values

            # Convert to numpy and remove batch/channel dimensions
            ad_shap_np = ad_shap.squeeze().cpu().numpy()

            return ad_shap_np

        except Exception as e:
            logger.error(f"Error generating ADD SHAP explanation: {e}")
            logger.info("Falling back to gradient-based explanation...")
            return self._gradient_explanation(mri_data, 'ADD')
    
    def explain_cog_prediction(self, mri_data: np.ndarray) -> np.ndarray:
        """
        Generate SHAP explanation for COG prediction

        Args:
            mri_data: Preprocessed MRI data (182, 218, 182)

        Returns:
            SHAP values for cognitive score (182, 218, 182)
        """
        if self.use_gradients or self.cog_explainer is None:
            logger.info("Using gradient-based explanation for COG prediction...")
            return self._gradient_explanation(mri_data, 'COG')

        try:
            # Prepare input tensor
            if len(mri_data.shape) == 3:
                input_tensor = torch.from_numpy(mri_data).float().unsqueeze(0).unsqueeze(0)
            else:
                input_tensor = torch.from_numpy(mri_data).float()

            input_tensor = input_tensor.to(self.device)

            # Generate SHAP values
            logger.info("Generating SHAP values for COG prediction...")
            shap_values = self.cog_explainer.shap_values(input_tensor)

            # Convert to numpy and remove batch/channel dimensions
            if isinstance(shap_values, list):
                cog_shap_np = shap_values[0].squeeze().cpu().numpy()
            else:
                cog_shap_np = shap_values.squeeze().cpu().numpy()

            return cog_shap_np

        except Exception as e:
            logger.error(f"Error generating COG SHAP explanation: {e}")
            logger.info("Falling back to gradient-based explanation...")
            return self._gradient_explanation(mri_data, 'COG')
    
    def create_heatmap_overlay(self, 
                              mri_data: np.ndarray, 
                              shap_values: np.ndarray,
                              threshold_percentile: float = 95,
                              colormap: str = 'hot') -> Tuple[np.ndarray, np.ndarray]:
        """
        Create heatmap overlay on MRI scan
        
        Args:
            mri_data: Original MRI data (182, 218, 182)
            shap_values: SHAP values (182, 218, 182)
            threshold_percentile: Percentile threshold for showing heatmap
            colormap: Matplotlib colormap name
            
        Returns:
            Tuple of (heatmap_mask, overlay_image)
        """
        try:
            # Normalize SHAP values
            shap_abs = np.abs(shap_values)
            
            # Apply threshold
            threshold = np.percentile(shap_abs, threshold_percentile)
            heatmap_mask = shap_abs > threshold
            
            # Create normalized heatmap
            heatmap_normalized = np.zeros_like(shap_abs)
            if np.max(shap_abs) > 0:
                heatmap_normalized = shap_abs / np.max(shap_abs)
            
            # Apply mask
            heatmap_normalized = heatmap_normalized * heatmap_mask
            
            # Create overlay
            overlay = self._create_overlay_image(mri_data, heatmap_normalized, colormap)
            
            return heatmap_mask, overlay
            
        except Exception as e:
            logger.error(f"Error creating heatmap overlay: {e}")
            raise
    
    def _create_overlay_image(self, 
                             mri_data: np.ndarray, 
                             heatmap: np.ndarray, 
                             colormap: str) -> np.ndarray:
        """
        Create overlay image combining MRI and heatmap
        
        Args:
            mri_data: Original MRI data
            heatmap: Normalized heatmap data
            colormap: Matplotlib colormap name
            
        Returns:
            Overlay image
        """
        # Normalize MRI data for display
        mri_normalized = (mri_data - np.min(mri_data)) / (np.max(mri_data) - np.min(mri_data))
        
        # Create colormap
        cmap = plt.get_cmap(colormap)
        
        # Apply colormap to heatmap
        heatmap_colored = cmap(heatmap)
        
        # Create overlay (alpha blending)
        alpha = 0.6  # Transparency of heatmap
        overlay = np.zeros((*mri_data.shape, 3))
        
        # Convert grayscale MRI to RGB
        mri_rgb = np.stack([mri_normalized] * 3, axis=-1)
        
        # Blend images
        mask = heatmap > 0
        overlay = mri_rgb.copy()
        overlay[mask] = (1 - alpha) * mri_rgb[mask] + alpha * heatmap_colored[mask, :3]
        
        return overlay
    
    def generate_slice_visualizations(self, 
                                    mri_data: np.ndarray, 
                                    shap_values: np.ndarray,
                                    slice_indices: Optional[Dict[str, int]] = None) -> Dict[str, np.ndarray]:
        """
        Generate slice visualizations with SHAP overlays
        
        Args:
            mri_data: Original MRI data (182, 218, 182)
            shap_values: SHAP values (182, 218, 182)
            slice_indices: Dictionary with slice indices for each view
            
        Returns:
            Dictionary with slice visualizations
        """
        if slice_indices is None:
            slice_indices = {
                'axial': mri_data.shape[2] // 2,
                'sagittal': mri_data.shape[0] // 2,
                'coronal': mri_data.shape[1] // 2
            }
        
        visualizations = {}
        
        try:
            # Create heatmap overlay
            heatmap_mask, overlay = self.create_heatmap_overlay(mri_data, shap_values)
            
            # Extract slices
            visualizations['axial'] = {
                'mri': mri_data[:, :, slice_indices['axial']],
                'overlay': overlay[:, :, slice_indices['axial']],
                'heatmap': heatmap_mask[:, :, slice_indices['axial']]
            }
            
            visualizations['sagittal'] = {
                'mri': mri_data[slice_indices['sagittal'], :, :],
                'overlay': overlay[slice_indices['sagittal'], :, :],
                'heatmap': heatmap_mask[slice_indices['sagittal'], :, :]
            }
            
            visualizations['coronal'] = {
                'mri': mri_data[:, slice_indices['coronal'], :],
                'overlay': overlay[:, slice_indices['coronal'], :],
                'heatmap': heatmap_mask[:, slice_indices['coronal'], :]
            }
            
            return visualizations
            
        except Exception as e:
            logger.error(f"Error generating slice visualizations: {e}")
            raise
    
    def explain_full(self, mri_data: np.ndarray) -> Dict:
        """
        Generate full SHAP explanation for both ADD and COG predictions
        
        Args:
            mri_data: Preprocessed MRI data (182, 218, 182)
            
        Returns:
            Dictionary with SHAP explanations and visualizations
        """
        results = {}
        
        try:
            # Generate SHAP explanations
            if self.add_explainer is not None:
                add_shap = self.explain_add_prediction(mri_data)
                results['add_shap_values'] = add_shap
                results['add_visualizations'] = self.generate_slice_visualizations(mri_data, add_shap)
            
            if self.cog_explainer is not None:
                cog_shap = self.explain_cog_prediction(mri_data)
                results['cog_shap_values'] = cog_shap
                results['cog_visualizations'] = self.generate_slice_visualizations(mri_data, cog_shap)
            
            return results
            
        except Exception as e:
            logger.error(f"Error generating full SHAP explanation: {e}")
            raise


def create_shap_explainer(classifier: NCOMMSClassifier, 
                         background_samples: Optional[np.ndarray] = None) -> NCOMMSSHAPExplainer:
    """
    Convenience function to create SHAP explainer
    
    Args:
        classifier: Trained classifier
        background_samples: Background samples for SHAP
        
    Returns:
        Initialized SHAP explainer
    """
    return NCOMMSSHAPExplainer(classifier, background_samples)
