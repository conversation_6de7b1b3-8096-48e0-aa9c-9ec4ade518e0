"""
Test Frontend Components
Quick test of all frontend components without Streamlit
"""

import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import tempfile
import os

# Import our components
from ncomms2022_preprocessing_fsl import NCOMMSFSLPreprocessor
from ncomms2022_model_enhanced import NCOMMSClassifier
from ncomms2022_shap import NCOM<PERSON><PERSON>HA<PERSON>Explainer

def test_frontend_workflow():
    """Test the complete frontend workflow"""
    print("🧠 Testing Frontend Workflow")
    print("=" * 50)
    
    # Initialize components
    print("1. Initializing components...")
    preprocessor = NCOMMSFSLPreprocessor()
    classifier = NCOMMSClassifier()
    explainer = NCOMMSSHAPExplainer(classifier)
    print("✓ All components initialized")
    
    # Test with demo data
    demo_file = "ncomms2022_original/demo/mri/demo1.npy"
    print(f"\n2. Loading test data: {demo_file}")
    mri_data = np.load(demo_file)
    print(f"✓ Data loaded: {mri_data.shape}")
    
    # Test classification
    print("\n3. Running classification...")
    results = classifier.predict_full(mri_data)
    print(f"✓ Prediction: {results['predicted_class']} (confidence: {results['confidence']:.3f})")
    print(f"✓ Risk level: {results['risk_level']}")
    print(f"✓ COG score: {results['cog_score']:.2f}")
    print(f"✓ CN probability: {results['cn_probability']:.3f}")
    print(f"✓ AD probability: {results['ad_probability']:.3f}")
    
    # Test interpretability
    print("\n4. Generating interpretability maps...")
    shap_results = explainer.explain_full(mri_data)
    print(f"✓ SHAP results generated")
    
    # Test visualization creation
    print("\n5. Creating visualizations...")
    
    # Create MRI visualization
    fig, axes = plt.subplots(1, 3, figsize=(15, 5))
    
    # Axial slice
    axes[0].imshow(mri_data[:, :, mri_data.shape[2]//2], cmap='gray', origin='lower')
    axes[0].set_title('Axial View')
    axes[0].axis('off')
    
    # Sagittal slice
    axes[1].imshow(mri_data[mri_data.shape[0]//2, :, :], cmap='gray', origin='lower')
    axes[1].set_title('Sagittal View')
    axes[1].axis('off')
    
    # Coronal slice
    axes[2].imshow(mri_data[:, mri_data.shape[1]//2, :], cmap='gray', origin='lower')
    axes[2].set_title('Coronal View')
    axes[2].axis('off')
    
    plt.suptitle('MRI Visualization Test')
    plt.tight_layout()
    plt.savefig('test_mri_visualization.png', dpi=150, bbox_inches='tight')
    plt.close()
    print("✓ MRI visualization saved: test_mri_visualization.png")
    
    # Test with radiologist cohort data
    print("\n6. Testing with radiologist cohort data...")
    test_files = [
        "/mnt/z/radiologist_test_cohort_25/T1_ALZHEIMERS_demo_case1.npy",
        "/mnt/z/radiologist_test_cohort_25/T1_NORMAL_demo_case3.npy"
    ]
    
    for i, test_file in enumerate(test_files):
        if os.path.exists(test_file):
            print(f"\n   Testing file {i+1}: {os.path.basename(test_file)}")
            test_data = np.load(test_file)
            test_results = classifier.predict_full(test_data)
            print(f"   ✓ Prediction: {test_results['predicted_class']} (confidence: {test_results['confidence']:.3f})")
            print(f"   ✓ Risk: {test_results['risk_level']}, COG: {test_results['cog_score']:.2f}")
        else:
            print(f"   ⚠ File not found: {test_file}")
    
    print("\n" + "=" * 50)
    print("🎉 Frontend workflow test completed successfully!")
    print("✓ All components working correctly")
    print("✓ Ready for Streamlit deployment")
    
    return True

def create_requirements_file():
    """Create requirements.txt for easy deployment"""
    requirements = """
streamlit>=1.28.0
torch>=2.0.0
torchvision>=0.15.0
numpy>=1.24.0
pandas>=2.0.0
matplotlib>=3.7.0
scikit-learn>=1.3.0
scipy>=1.11.0
nibabel>=5.1.0
nilearn>=0.10.0
shap>=0.41.0
tqdm>=4.65.0
Pillow>=10.0.0
"""
    
    with open('requirements_frontend.txt', 'w') as f:
        f.write(requirements.strip())
    
    print("✓ Created requirements_frontend.txt")

def create_deployment_script():
    """Create deployment script"""
    script_content = """#!/bin/bash
# Demetify NCOMMS2022 Deployment Script

echo "🧠 Demetify - NCOMMS2022 Frontend Deployment"
echo "============================================="

# Check if conda is available
if ! command -v conda &> /dev/null; then
    echo "❌ Conda not found. Please install Miniconda/Anaconda first."
    exit 1
fi

# Activate environment
echo "📦 Activating conda environment 'abstract'..."
eval "$(conda shell.bash hook)"
conda activate abstract

# Install requirements
echo "📦 Installing Python requirements..."
pip install -r requirements_frontend.txt

# Check if models exist
if [ ! -d "ncomms2022_original/checkpoint_dir" ]; then
    echo "❌ Model checkpoints not found. Please ensure ncomms2022_original is properly set up."
    exit 1
fi

echo "✅ Setup complete!"
echo ""
echo "🚀 Starting Demetify Frontend..."
echo "   Access the application at: http://localhost:8501"
echo "   Press Ctrl+C to stop the server"
echo ""

# Start Streamlit
streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address 0.0.0.0
"""
    
    with open('deploy_demetify.sh', 'w') as f:
        f.write(script_content)
    
    # Make executable
    os.chmod('deploy_demetify.sh', 0o755)
    print("✓ Created deploy_demetify.sh")

def create_windows_batch():
    """Create Windows batch file for deployment"""
    batch_content = """@echo off
REM Demetify NCOMMS2022 Windows Deployment Script

echo 🧠 Demetify - NCOMMS2022 Frontend Deployment
echo =============================================

REM Check if conda is available
where conda >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ Conda not found. Please install Miniconda/Anaconda first.
    pause
    exit /b 1
)

REM Activate environment
echo 📦 Activating conda environment 'abstract'...
call conda activate abstract

REM Install requirements
echo 📦 Installing Python requirements...
pip install -r requirements_frontend.txt

REM Check if models exist
if not exist "ncomms2022_original\\checkpoint_dir" (
    echo ❌ Model checkpoints not found. Please ensure ncomms2022_original is properly set up.
    pause
    exit /b 1
)

echo ✅ Setup complete!
echo.
echo 🚀 Starting Demetify Frontend...
echo    Access the application at: http://localhost:8501
echo    Press Ctrl+C to stop the server
echo.

REM Start Streamlit
streamlit run demetify_ncomms2022_app.py --server.port 8501 --server.address 0.0.0.0

pause
"""
    
    with open('deploy_demetify.bat', 'w') as f:
        f.write(batch_content)
    
    print("✓ Created deploy_demetify.bat")

if __name__ == "__main__":
    # Run tests
    success = test_frontend_workflow()
    
    if success:
        print("\n📝 Creating deployment files...")
        create_requirements_file()
        create_deployment_script()
        create_windows_batch()
        
        print("\n🎯 DEPLOYMENT READY!")
        print("=" * 50)
        print("To launch the frontend:")
        print("  Linux/Mac: ./deploy_demetify.sh")
        print("  Windows:   deploy_demetify.bat")
        print("  Manual:    streamlit run demetify_ncomms2022_app.py")
        print("")
        print("📁 Files created:")
        print("  - requirements_frontend.txt")
        print("  - deploy_demetify.sh")
        print("  - deploy_demetify.bat")
        print("  - test_mri_visualization.png")
